# Диагностични поправки за Progress Bar и Session проблемите

## Проблеми които се адресират

### 1. ❌ **Progress Bar UI не се показва визуално**
**Симптоми**: JavaScript кодът се изпълнява, но progress bar не се вижда на страницата
**Диагностични мерки**:
- Добавена подробна DOM диагностика
- Принудително задаване на CSS стилове
- Временни визуални маркери (червен фон, синя граница)
- Проверка на позициониране и родителски елементи

### 2. ❌ **AJAX заявки към processBatch връщат login страница**
**Симптоми**: User token изтича по време на експорта, сесията се прекъсва
**Диагностични мерки**:
- Подробно логване на response статуси и headers
- Автоматично откриване на login redirect
- Механизъм за refresh на user token
- Retry логика при session проблеми

## Направени диагностични промени

### 1. DOM Диагностика за Progress Bar

```javascript
// ДОБАВЕНО: Подробна диагностика на DOM елемента
setTimeout(() => {
    const addedElement = document.getElementById('large-export-progress');
    if (addedElement) {
        console.log('Progress Bar Debug: ✅ DOM елементът е създаден успешно');
        console.log('Progress Bar Debug: Element position:', addedElement.getBoundingClientRect());
        console.log('Progress Bar Debug: Element styles:', window.getComputedStyle(addedElement));
        console.log('Progress Bar Debug: Element parent:', addedElement.parentElement);
        console.log('Progress Bar Debug: Element visibility:', addedElement.style.display, addedElement.style.visibility);
        
        // Принудително правим елемента видим за тестване
        addedElement.style.display = 'block';
        addedElement.style.visibility = 'visible';
        addedElement.style.opacity = '1';
        addedElement.style.zIndex = '9999';
        addedElement.style.backgroundColor = 'red'; // Временно за видимост
        addedElement.style.border = '3px solid blue'; // Временно за видимост
        
        console.log('Progress Bar Debug: ✅ Принудително направен видим');
    } else {
        console.error('Progress Bar Debug: ❌ DOM елементът НЕ е намерен след добавяне!');
    }
}, 100);
```

### 2. Принудително CSS стилизиране

```javascript
// ДОБАВЕНО: Принудително задаване на CSS стилове
progressContainer.style.cssText = `
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1000 !important;
    background-color: white !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 8px !important;
    padding: 24px !important;
    margin-bottom: 24px !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    width: 100% !important;
    max-width: none !important;
`;
```

### 3. Подробна Response диагностика

```javascript
// ДОБАВЕНО: Подробно логване на AJAX response
.then(response => {
    console.log('Batch Debug: Response URL:', response.url);
    console.log('Batch Debug: Response status:', response.status);
    console.log('Batch Debug: Response headers:', response.headers);
    
    // Проверяваме дали response е redirect към login
    if (response.url && response.url.includes('login')) {
        console.error('Batch Debug: ❌ Redirect към login страница!');
        throw new Error('Сесията е изтекла. Моля влезте отново в системата.');
    }
    
    // Проверяваме статус кода
    if (response.status === 302 || response.status === 401) {
        console.error('Batch Debug: ❌ Unauthorized или redirect status:', response.status);
        throw new Error('Сесията е изтекла. Моля влезте отново в системата.');
    }
    
    // Проверяваме дали response е JSON
    const contentType = response.headers.get('content-type');
    console.log('Batch Debug: Content-Type:', contentType);
    
    if (!contentType || !contentType.includes('application/json')) {
        console.error('Batch Debug: ❌ Неочакван Content-Type:', contentType);
        
        // Опитваме се да прочетем response като текст за диагностика
        return response.text().then(text => {
            console.error('Batch Debug: Response text:', text.substring(0, 500));
            if (text.includes('login') || text.includes('Потребителско име')) {
                throw new Error('Сесията е изтекла. Моля влезте отново в системата.');
            }
            throw new Error('Неочакван response тип. Възможно е сесията да е изтекла.');
        });
    }
    
    return response.json();
})
```

### 4. User Token Refresh механизъм

```javascript
// ДОБАВЕНО: Двустепенен token refresh механизъм
productExportRefreshUserToken: function() {
    console.log('Token Debug: Опит за обновяване на user token');
    
    // Първо опитваме стандартния refresh endpoint
    return fetch('index.php?route=common/login/refresh', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cache-Control': 'no-cache'
        },
        cache: 'no-store'
    })
    .then(response => {
        if (response.ok) {
            return response.json();
        } else {
            throw new Error('Refresh endpoint не е наличен');
        }
    })
    .then(data => {
        if (data.user_token) {
            console.log('Token Debug: ✅ Успешно обновен user token чрез refresh');
            // Обновяваме token-а в hidden полето
            const tokenInput = document.querySelector('input[name="user_token"]');
            if (tokenInput) {
                tokenInput.value = data.user_token;
            }
            return data.user_token;
        } else {
            throw new Error('Неуспешно обновяване на token');
        }
    })
    .catch(error => {
        console.log('Token Debug: Refresh endpoint неуспешен, опитваме алтернативен подход');
        
        // Алтернативен подход - опитваме се да получим нов token чрез dashboard заявка
        return fetch('index.php?route=common/dashboard', {
            method: 'GET',
            cache: 'no-store',
            headers: { 'Cache-Control': 'no-cache' }
        })
        .then(response => response.text())
        .then(html => {
            // Търсим user_token в HTML-а
            const tokenMatch = html.match(/user_token['"]\s*:\s*['"]([^'"]+)['"]/);
            if (tokenMatch && tokenMatch[1]) {
                const newToken = tokenMatch[1];
                console.log('Token Debug: ✅ Успешно извлечен token от dashboard');
                
                // Обновяваме token-а в hidden полето
                const tokenInput = document.querySelector('input[name="user_token"]');
                if (tokenInput) {
                    tokenInput.value = newToken;
                }
                return newToken;
            } else {
                throw new Error('Не може да се извлече нов token');
            }
        })
        .catch(altError => {
            console.error('Token Debug: ❌ И алтернативният подход неуспешен:', altError);
            throw new Error('Неуспешно обновяване на token с всички методи');
        });
    });
}
```

### 5. Retry логика при session проблеми

```javascript
// ДОБАВЕНО: Автоматичен retry при session проблеми
.catch(error => {
    console.error('Batch Debug: Грешка при обработка на batch:', error);
    
    // Ако грешката е свързана със сесия, опитваме token refresh
    if (error.message.includes('Сесията е изтекла') || error.message.includes('Неочакван response тип')) {
        console.log('Batch Debug: Опит за token refresh и retry на batch');
        
        this.productExportRefreshUserToken()
            .then(() => {
                console.log('Batch Debug: ✅ Token обновен, retry на batch');
                // Retry същия batch с новия token
                setTimeout(() => {
                    this.productExportProcessNextBatch();
                }, 1000);
            })
            .catch(tokenError => {
                console.error('Batch Debug: ❌ Неуспешен token refresh:', tokenError);
                this.productExportShowError(`Сесията е изтекла и не може да бъде обновена. Моля влезте отново в системата.`);
                this.productExportCancelLargeExport();
            });
    } else {
        // Други грешки - спираме експорта
        this.productExportShowError('Грешка при обработка на batch: ' + error.message);
        this.productExportCancelLargeExport();
    }
});
```

## Тестов план за диагностика

### Стъпка 1: Тествайте Progress Bar видимостта
1. Започнете експорт на всички продукти
2. Отворете F12 → Console
3. Търсете съобщения "Progress Bar Debug:"
4. Проверете дали виждате червен фон и синя граница (временни маркери)
5. Проверете F12 → Elements за DOM елемента с id="large-export-progress"

### Стъпка 2: Тествайте Session диагностиката
1. Започнете експорт
2. Наблюдавайте Console за "Batch Debug:" съобщения
3. Проверете F12 → Network таба за processBatch заявките
4. Наблюдавайте дали има "Token Debug:" съобщения при проблеми

### Стъпка 3: Анализирайте резултатите
1. **Ако progress bar се вижда с червен фон** → DOM създаването работи, проблемът е в CSS
2. **Ако progress bar не се вижда въобще** → DOM създаването има проблем
3. **Ако виждате "Token Debug:" съобщения** → Session проблемът се адресира автоматично
4. **Ако processBatch заявките връщат HTML вместо JSON** → Token refresh трябва да се задейства

## Очаквани резултати

### ✅ **При успешна диагностика ще видите:**
- Progress bar с червен фон и синя граница (временно)
- Подробни Console логове за всяка стъпка
- Автоматичен token refresh при session проблеми
- Успешно завършване на експорта

### ❌ **При проблеми ще видите:**
- Ясни error съобщения в Console
- Подробна информация за причината на проблема
- Конкретни стъпки за решение

## Следващи стъпки

1. **Тествайте с новите диагностични промени**
2. **Споделете Console логовете** за анализ
3. **Проверете Network таба** за response детайли
4. **Докладвайте резултатите** за финални корекции

Тези промени ще ни дадат пълна картина за причините на проблемите и ще позволят точни корекции.

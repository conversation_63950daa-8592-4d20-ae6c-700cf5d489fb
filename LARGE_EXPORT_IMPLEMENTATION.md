# Имплементация на динамично разделяне на експорта

## Преглед

Имплементирано е решение за обработка на големи експорти с динамично разделяне на AJAX заявки и progress bar интерфейс за предотвратяване на memory crash-ове.

## Основни промени

### 1. Контролер (Export.php)

**Нови константи:**
- `$largeBatchSize = 75` - По-малък batch размер за големи експорти
- `$largeExportThreshold = 1000` - Праг за големи експорти

**Нови методи:**
- `initiateLargeExport()` - Инициира голям експорт с batch обработка
- `processBatch()` - Обработва един batch от голям експорт
- `finalizeLargeExport()` - Финализира големия експорт
- `getProductsDataByIds()` - Получава данни за продукти по ID-та
- `generateBatchFile()` - Генерира временен файл за един batch
- `combineBatchFiles()` - Обединява всички batch файлове
- `combineCsvFiles()` - Обединява CSV файлове
- `combineXlsxFiles()` - Обединява XLSX файлове (копира първия)
- `combineXmlFiles()` - Обединява XML файлове
- `cleanupTempFiles()` - Изчиства временните файлове
- `returnFinalExportFile()` - Връща финалния експорт файл
- `logMemoryUsage()` - Логва използването на паметта

**Логика за автоматично определяне на режима:**
```php
if ($productCount > $this->largeExportThreshold) {
    $this->initiateLargeExport($products, $exportFormat, $exportData);
} else {
    $this->generateAndReturnExportFile($exportModel, $products, $exportFormat, $exportData);
}
```

### 2. JavaScript (product-export.js)

**Нови методи:**
- `productExportHandleLargeExport()` - Обработва голям експорт
- `productExportShowProgressBar()` - Показва progress bar интерфейса
- `productExportProcessBatches()` - Обработва batch-овете последователно
- `productExportProcessNextBatch()` - Обработва следващия batch
- `productExportFinalizeLargeExport()` - Финализира големия експорт
- `productExportUpdateProgress()` - Обновява progress bar-а
- `productExportUpdateMemoryInfo()` - Обновява информацията за паметта
- `productExportCancelLargeExport()` - Отказва големия експорт
- `productExportHideProgressBar()` - Скрива progress bar

**Progress bar интерфейс:**
- Показва прогрес в проценти
- Информация за обработените/общо продукти
- Batch информация
- Memory usage информация
- Възможност за отказване

### 3. Модел (Productexportdata.php)

**Подобрения:**
- Добавен `$largeBatchSize = 75` за големи експорти
- Методи `setBatchSize()` и `getBatchSize()` за динамично управление
- Подобрено memory management с `getMemoryLimitInBytes()`
- Критично ниво на памет (85%) с хвърляне на грешка
- Намалено sleep време между batch-овете (0.5 сек)

## Как работи

### Малки експорти (< 1000 продукта)
1. Директно генериране на файла
2. Веднага връщане на резултата
3. Без progress bar

### Големи експорти (≥ 1000 продукта)
1. Разделяне на batch-ове от 75 продукта
2. Показване на progress bar
3. Последователна обработка на batch-овете чрез AJAX
4. Генериране на временни файлове
5. Обединяване в финален файл
6. Автоматично изчистване

## Memory Management

### Превенция на crash-ове:
- Мониторинг на memory usage на всеки batch
- Принудително garbage collection
- Критично ниво на 85% с прекратяване
- Логване на memory usage за debug

### Оптимизации:
- По-малък batch размер (75 вместо 150)
- Explicit unset() на променливи
- Намалено sleep време между batch-овете
- Cleanup на временни файлове

## Тестване

### За тестване на малки експорти:
1. Изберете под 1000 продукта
2. Експортът трябва да работи директно без progress bar

### За тестване на големи експорти:
1. Изберете над 1000 продукта или не избирайте нищо (всички продукти)
2. Трябва да се покаже progress bar
3. Наблюдавайте прогреса в реално време
4. Проверете memory информацията

### Debug информация:
- Включете developer режим за подробни логове
- Наблюдавайте memory usage в progress bar-а
- Проверете временните файлове в `DIR_UPLOAD . 'export_temp/'`

## Файлове променени:
1. `system/storage/theme/Backend/Controller/Catalog/Product/Export.php`
2. `system/storage/theme/Backend/View/Javascript/product-export.js`
3. `system/storage/theme/Backend/Model/Catalog/Productexportdata.php`

## Backup файлове създадени:
- `Export.php.backup.YYYYMMDD_HHMMSS`
- `product-export.js.backup.YYYYMMDD_HHMMSS`
- `Productexportdata.php.backup.YYYYMMDD_HHMMSS`

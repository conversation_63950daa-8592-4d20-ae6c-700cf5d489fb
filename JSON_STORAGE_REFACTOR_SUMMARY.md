# Рефакториране на Export контролера - JSON файлове вместо PHP сесии

## Обзор на промените

Успешно рефакторирах всички методи в `system/storage/theme/Backend/Controller/Catalog/Product/Export.php` да използват JSON файлове вместо PHP сесии за съхранение на данни за експорта.

## Причина за промяната

**Проблем**: PHP сесията изтичаше по време на дълги export операции (9777 продукта, 131 batch-а), което причиняваше login redirect вместо JSON response в `processBatch()` метода.

**Решение**: JSON файлове са независими от session lifetime и осигуряват стабилно съхранение на данни за дълги операции.

## Добавени helper методи

### 1. Инициализация и cleanup
```php
private $exportCacheDir = '';

private function initializeExportCacheDir() {
    // Създава DIR_CACHE . 'export/' директория
    // Почиства стари файлове (по-стари от 24 часа)
}

private function cleanupOldExportFiles() {
    // Автоматично изтрива JSON файлове по-стари от 24 часа
}
```

### 2. JSON файл операции
```php
private function saveExportData($exportId, $data) {
    // Записва export данни в JSON файл с file locking
    // Добавя timestamp за tracking
    // Връща true/false за успех
}

private function loadExportData($exportId) {
    // Чете export данни от JSON файл с file locking
    // Graceful handling на липсващи/корумпирани файлове
    // Връща array или null
}

private function deleteExportData($exportId) {
    // Изтрива JSON файл след завършване на експорта
}
```

## Рефакторирани методи

### 1. `initiateLargeExport()` метод

**ПРЕДИ** (използваше сесии):
```php
$this->setSession('large_export_' . $exportId, [
    'format' => $exportFormat,
    'export_data' => $exportData,
    'batches' => $batches,
    // ...
]);
```

**СЕГА** (използва JSON файлове):
```php
$exportDataToSave = [
    'export_id' => $exportId,
    'format' => $exportFormat,
    'export_data' => $exportData,
    'batches' => $batches,
    'total_batches' => $totalBatches,
    'total_products' => count($productIds),
    'batch_size' => $this->largeBatchSize,
    'processed_batches' => 0,
    'processed_products' => 0,
    'temp_files' => [],
    'started_at' => time(),
    'status' => 'initiated'
];

if (!$this->saveExportData($exportId, $exportDataToSave)) {
    throw new \Exception('Не може да се запишат данните за експорта');
}
```

### 2. `processBatch()` метод

**ПРЕДИ** (използваше сесии):
```php
if (empty($exportId) || !$this->getSession('large_export_' . $exportId)) {
    throw new \Exception('Невалиден експорт ID или изтекла сесия');
}

$exportInfo = $this->getSession('large_export_' . $exportId);

// Обновяване на прогреса
$this->setSession('large_export_' . $exportId, [
    'processed_batches' => $batchIndex + 1,
    // ...
]);
```

**СЕГА** (използва JSON файлове):
```php
// Зареждаме export данните от JSON файл
$exportInfo = $this->loadExportData($exportId);
if (!$exportInfo) {
    throw new \Exception('Невалиден експорт ID или изтекли данни');
}

// Обновяваме информацията за прогреса в JSON файла
$exportInfo['processed_batches'] = $batchIndex + 1;
$exportInfo['processed_products'] += count($batchProducts);
$exportInfo['temp_files'][] = $tempFileName;
$exportInfo['status'] = 'processing';

if (!$this->saveExportData($exportId, $exportInfo)) {
    throw new \Exception('Не може да се запише прогресът на експорта');
}
```

### 3. `finalizeLargeExport()` метод

**ПРЕДИ** (използваше сесии):
```php
if (empty($exportId) || !$this->getSession('large_export_' . $exportId)) {
    throw new \Exception('Невалиден експорт ID или изтекла сесия');
}

$exportInfo = $this->getSession('large_export_' . $exportId);

// Изчистваме данните от сесията
$this->setSession('large_export_' . $exportId, null);
```

**СЕГА** (използва JSON файлове):
```php
// Зареждаме export данните от JSON файл
$exportInfo = $this->loadExportData($exportId);
if (!$exportInfo) {
    throw new \Exception('Невалиден експорт ID или изтекли данни');
}

// Обновяваме статуса на експорта
$exportInfo['status'] = 'finalizing';
$this->saveExportData($exportId, $exportInfo);

// След успешно завършване
$exportInfo['status'] = 'completed';
$exportInfo['final_file'] = $finalFileName;
$exportInfo['completed_at'] = time();
$this->saveExportData($exportId, $exportInfo);

// Изчистваме JSON файла
$this->deleteExportData($exportId);
```

### 4. Нов метод `checkExportStatus()`

```php
public function checkExportStatus() {
    // Позволява проверка на статуса на export операция
    // Връща подробна информация за прогреса
    // Полезно за monitoring и debugging
}
```

## JSON файл структура

```json
{
    "export_id": "export_67867...",
    "format": "xlsx",
    "export_data": { /* export настройки */ },
    "batches": [ /* масиви с product IDs */ ],
    "total_batches": 131,
    "total_products": 9777,
    "batch_size": 75,
    "processed_batches": 45,
    "processed_products": 3375,
    "temp_files": [ /* временни файлове */ ],
    "started_at": 1752592809,
    "last_updated": 1752593156,
    "status": "processing",
    "created_at": 1752592809
}
```

## Статуси на експорта

- `initiated` - Експортът е започнат
- `processing` - Batch обработка в ход
- `finalizing` - Обединяване на файлове
- `completed` - Успешно завършен
- `error` - Грешка по време на експорта

## Предимства на JSON файловете

### ✅ **Session Independence**
- Независими от PHP session lifetime
- Няма проблеми с session timeout
- Стабилни за дълги операции

### ✅ **Persistence**
- Данните се запазват при restart на сървъра
- Възможност за recovery при crash
- Audit trail на операциите

### ✅ **Concurrent Access**
- File locking за безопасен достъп
- Множество процеси могат да четат едновременно
- Atomic write операции

### ✅ **Debugging & Monitoring**
- Лесно четими JSON файлове
- Подробен status tracking
- Възможност за external monitoring

### ✅ **Automatic Cleanup**
- Автоматично изтриване на стари файлове
- Няма memory leaks в сесиите
- Контролиран disk usage

## Backward Compatibility

- Всички публични методи запазват същите сигнатури
- JavaScript кодът не се променя
- Същите JSON responses към frontend
- Запазена функционалност на batch обработката

## Error Handling

- Graceful handling на липсващи JSON файлове
- Proper error logging при file операции
- Status tracking при грешки
- Automatic cleanup при проблеми

## Тестов план

### Стъпка 1: Основна функционалност
1. Започнете експорт на всички продукти
2. Проверете че се създава JSON файл в `DIR_CACHE/export/`
3. Наблюдавайте batch обработката
4. Потвърдете успешното завършване

### Стъпка 2: Session Independence
1. Започнете дълъг експорт
2. Изчакайте няколко batch-а
3. Проверете че няма login redirect
4. Потвърдете че експортът продължава стабилно

### Стъпка 3: Recovery Testing
1. Започнете експорт
2. Прекъснете процеса по средата
3. Проверете JSON файла за състоянието
4. Тествайте recovery механизмите

## Следващи стъпки

1. **Тествайте пълния експорт** на 9777 продукта
2. **Проверете JSON файловете** в `DIR_CACHE/export/`
3. **Наблюдавайте Console логовете** за грешки
4. **Потвърдете стабилната batch обработка**
5. **Докладвайте резултатите**

Тази рефакторизация трябва да реши напълно проблема със session timeout и да осигури стабилни дълги export операции!

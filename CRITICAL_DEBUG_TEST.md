# КРИТИЧЕН DEBUG ТЕСТ за Export функционалността

## Направени промени за диагностика

### 1. ПРИНУДИТЕЛНО задействане на large export режим
- **Нов праг**: 100 продукта (вместо 500)
- **Цел**: Гарантиран<PERSON> задействане с 9777 продукта

### 2. Критични debug съобщения
- **PHP error_log**: Директно записване в error лога
- **JavaScript alert**: Директни popup съобщения
- **Подробно логване**: На всяка стъпка от процеса

### 3. Проверки за headers
- Дали headers са изпратени преди JSON response
- Дали има конфликти с изходи

## Какво да търсите при тестване

### 1. В PHP error лога:
```
CRITICAL DEBUG: Преди проверка - productCount: 9777, threshold: 500
CRITICAL DEBUG: Условието за принудително задействане е TRUE!
CRITICAL DEBUG: initiateLargeExport() е извикан с 9777 продукта
```

### 2. В браузера (Alert съобщения):
```
DEBUG: Получен JSON response - large_export: TRUE, error: NONE
DEBUG: Задействане на large export режим!
DEBUG: productExportHandleLargeExport() е извикана!
DEBUG: productExportShowProgressBar() е извикана!
```

### 3. В Browser Console:
```
Export Debug: Response Content-Type: application/json
Export Debug: Получен JSON response: {large_export: true, ...}
Large Export Debug: Започване на обработка на голям експорт
Progress Bar Debug: Показване на progress bar
```

## Тестов сценарий

### Стъпка 1: Подготовка
1. Отворете Developer Tools (F12)
2. Отидете на Console таба
3. Подгответе се за alert съобщения

### Стъпка 2: Започнете експорт
1. Отидете на Export страницата
2. НЕ избирайте категории или продукти (всички продукти)
3. Изберете формат (XLSX/CSV/XML)
4. Натиснете "Експорт"

### Стъпка 3: Наблюдавайте резултатите
1. **Първо**: Трябва да видите alert с "DEBUG: Получен JSON response"
2. **Второ**: Ако large_export е TRUE → alert "DEBUG: Задействане на large export режим!"
3. **Трето**: Alert "DEBUG: productExportHandleLargeExport() е извикана!"
4. **Четвърто**: Alert "DEBUG: productExportShowProgressBar() е извикана!"

## Възможни резултати и диагностика

### Резултат A: Няма alert съобщения
**Проблем**: JavaScript не получава JSON response
**Проверка**: 
- Проверете Network таба в Developer Tools
- Търсете заявката към export endpoint-а
- Проверете response-а

### Резултат B: Alert показва large_export: FALSE
**Проблем**: PHP не задейства large export режим
**Проверка**: 
- Проверете PHP error лога за "CRITICAL DEBUG" съобщения
- Ако няма такива съобщения → проблем в getProductsForExport()
- Ако има но условието е FALSE → проблем в логиката

### Резултат C: Alert показва large_export: TRUE но няма progress bar
**Проблем**: JavaScript не обработва правилно response-а
**Проверка**: 
- Проверете дали се показват следващите alert съобщения
- Проверете Console за грешки в JavaScript

### Резултат D: Всички alert съобщения се показват но няма progress bar UI
**Проблем**: DOM манипулация не работи
**Проверка**: 
- Проверете Console за JavaScript грешки
- Проверете дали HTML елементите се създават

## Файлове за проверка

### PHP Error Log:
- Основен error лог на сървъра
- Търсете "CRITICAL DEBUG" съобщения

### Browser Console:
- F12 → Console таб
- Търсете "Export Debug" съобщения

### Network Tab:
- F12 → Network таб
- Проверете заявката към export endpoint-а
- Проверете response Content-Type и данните

## Следващи стъпки според резултата

### Ако няма alert съобщения:
1. Проверете дали заявката се изпраща
2. Проверете дали има JavaScript грешки
3. Проверете дали правилният endpoint се извиква

### Ако има alert но large_export е FALSE:
1. Проверете PHP error лога
2. Проверете дали getProductsForExport() връща правилния брой
3. Проверете дали условието се изпълнява

### Ако large_export е TRUE но няма progress bar:
1. Проверете JavaScript грешки
2. Проверете дали функциите се извикват
3. Проверете DOM манипулацията

## ВАЖНО: Временни промени

Тези промени са ВРЕМЕННИ за диагностика:
- Принудителен праг от 100 продукта
- Alert съобщения в JavaScript
- Допълнителни error_log съобщения

След като намерим проблема, ще премахнем alert съобщенията и ще върнем нормалния праг.

## Очакван успешен резултат

При успешен тест трябва да видите:
1. ✅ Alert: "DEBUG: Получен JSON response - large_export: TRUE"
2. ✅ Alert: "DEBUG: Задействане на large export режим!"
3. ✅ Alert: "DEBUG: productExportHandleLargeExport() е извикана!"
4. ✅ Alert: "DEBUG: productExportShowProgressBar() е извикана!"
5. ✅ Progress bar се появява на страницата
6. ✅ Batch обработката започва

Ако всички тези стъпки се изпълнят, проблемът е решен!

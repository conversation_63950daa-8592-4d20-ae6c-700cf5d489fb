<?php

namespace Theme25\Backend\Model\Catalog;

/**
 * Оптимизиран модел за извличане на данни за експорт на продукти
 * Използва групирани заявки и batch обработка за подобрена производителност
 */
class Productexportdata extends \Model {

    private $batchSize = 150;
    private $largeBatchSize = 75; // По-малък batch размер за големи експорти
    private $languageId = 1;

    public function __construct($registry) {
        parent::__construct($registry);
        // language_id ще се зададе чрез setLanguageId() метод от контролера
        $this->languageId = $this->registry->get('languageId') ?? 1;
    }

    /**
     * Задава ID на езика за използване в заявките
     *
     * @param int $languageId ID на езика
     */
    public function setLanguageId($languageId) {
        $this->languageId = (int)$languageId;
    }

    /**
     * Получава текущо зададения ID на езика
     *
     * @return int
     */
    public function getLanguageId() {
        return $this->languageId;
    }

    /**
     * Задава batch размера за обработка
     *
     * @param int $batchSize Размер на batch-а
     */
    public function setBatchSize($batchSize) {
        $this->batchSize = (int)$batchSize;
    }

    /**
     * Получава текущия batch размер
     *
     * @return int
     */
    public function getBatchSize() {
        return $this->batchSize;
    }

    public function checkDatabaseInstance() {
        return is_callable([$this->db, 'query']);
    }

    /**
     * Извлича продукти по масив от ID-та с една заявка
     * 
     * @param array $productIds Масив от product_id
     * @return array Масив с продуктови данни
     */
    public function getProductsByIds($productIds) {
        if (empty($productIds)) {
            return [];
        }

        // Подготвяме ID-тата за заявката
        $productIdsList = implode(',', array_map('intval', $productIds));

        $sql = "SELECT p.product_id, p.model, p.sku, p.upc, p.ean, p.jan, p.isbn, p.mpn, 
                       p.location, p.quantity, p.stock_status_id, p.image, p.manufacturer_id, 
                       p.shipping, p.price, p.points, p.tax_class_id, p.date_available, 
                       p.weight, p.weight_class_id, p.length, p.width, p.height, p.length_class_id, 
                       p.subtract, p.minimum, p.sort_order, p.status, p.date_added, p.date_modified,
                       pd.name, pd.description, pd.tag, pd.meta_title, pd.meta_description, pd.meta_keyword,
                       m.name as manufacturer_name,
                       ss.name as stock_status_name
                FROM `". DB_PREFIX . "product` p
                LEFT JOIN `". DB_PREFIX . "product_description` pd ON (p.product_id = pd.product_id AND pd.language_id = '{$this->languageId}')
                LEFT JOIN `". DB_PREFIX . "manufacturer` m ON (p.manufacturer_id = m.manufacturer_id)
                LEFT JOIN `". DB_PREFIX . "stock_status` ss ON (p.stock_status_id = ss.stock_status_id AND ss.language_id = '{$this->languageId}')
                WHERE p.product_id IN ({$productIdsList})
                ORDER BY p.product_id";

        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * Извлича продукти от категории с една заявка (включително подкатегории)
     * 
     * @param array $categoryIds Масив от category_id
     * @param bool $includeSubcategories Дали да включи подкатегории
     * @return array Масив с продуктови данни
     */
    public function getProductsByCategories($categoryIds, $includeSubcategories = true) {
        if (empty($categoryIds)) {
            return [];
        }

        $allCategoryIds = $categoryIds;

        // Ако включваме подкатегории, намираме всички подкатегории
        if ($includeSubcategories) {
            $allCategoryIds = $this->getAllSubcategoryIds($categoryIds);
        }

        $categoryIdsList = implode(',', array_map('intval', $allCategoryIds));

        $sql = "SELECT DISTINCT p.product_id, p.model, p.sku, p.upc, p.ean, p.jan, p.isbn, p.mpn, 
                       p.location, p.quantity, p.stock_status_id, p.image, p.manufacturer_id, 
                       p.shipping, p.price, p.points, p.tax_class_id, p.date_available, 
                       p.weight, p.weight_class_id, p.length, p.width, p.height, p.length_class_id, 
                       p.subtract, p.minimum, p.sort_order, p.status, p.date_added, p.date_modified,
                       pd.name, pd.description, pd.tag, pd.meta_title, pd.meta_description, pd.meta_keyword,
                       m.name as manufacturer_name,
                       ss.name as stock_status_name
                FROM `". DB_PREFIX . "product` p
                LEFT JOIN `". DB_PREFIX . "product_description` pd ON (p.product_id = pd.product_id AND pd.language_id = '{$this->languageId}')
                LEFT JOIN `". DB_PREFIX . "manufacturer` m ON (p.manufacturer_id = m.manufacturer_id)
                LEFT JOIN `". DB_PREFIX . "stock_status` ss ON (p.stock_status_id = ss.stock_status_id AND ss.language_id = '{$this->languageId}')
                INNER JOIN `". DB_PREFIX . "product_to_category` p2c ON (p.product_id = p2c.product_id)
                WHERE p2c.category_id IN ({$categoryIdsList})
                ORDER BY p.product_id";

        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * Извлича промоционални цени за масив от продукти с една заявка
     * 
     * @param array $productIds Масив от product_id
     * @return array Асоциативен масив [product_id => promo_price]
     */
    public function getProductPromotionalPrices($productIds) {
        if (empty($productIds)) {
            return [];
        }

        $productIdsList = implode(',', array_map('intval', $productIds));
        $currentDate = date('Y-m-d');

        $sql = "SELECT ps.product_id, ps.price as promo_price, ps.date_start, ps.date_end
                FROM `". DB_PREFIX . "product_special` ps
                WHERE ps.product_id IN ({$productIdsList})
                AND ps.customer_group_id = '1'
                AND (ps.date_start = '0000-00-00' OR ps.date_start <= '{$currentDate}')
                AND (ps.date_end = '0000-00-00' OR ps.date_end >= '{$currentDate}')
                ORDER BY ps.priority ASC, ps.price ASC";

        $query = $this->db->query($sql);
        
        $promoPrices = [];
        foreach ($query->rows as $row) {
            $productId = $row['product_id'];
            // Взимаме най-ниската активна промо цена за всеки продукт
            if (!isset($promoPrices[$productId]) || $row['promo_price'] < $promoPrices[$productId]) {
                $promoPrices[$productId] = (float)$row['promo_price'];
            }
        }

        return $promoPrices;
    }

    /**
     * Извлича описания за множество продукти и езици с една заявка
     *
     * @param array $productIds Масив от product_id
     * @param array $languageIds Масив от language_id (по подразбиране текущия език)
     * @return array Многомерен масив [product_id][language_id] => description_data
     */
    public function getProductDescriptions($productIds, $languageIds = null) {
        if (empty($productIds)) {
            return [];
        }

        if ($languageIds === null) {
            $languageIds = [$this->languageId];
        }

        if(count($languageIds) === 1 && $languageIds[0] != $this->languageId) {
            $languageIds = [$this->languageId];        
        }

        $productIdsList = implode(',', array_map('intval', $productIds));
        $languageIdsList = implode(',', array_map('intval', $languageIds));

        $sql = "SELECT pd.product_id, pd.language_id, pd.name, pd.description, pd.tag, 
                       pd.meta_title, pd.meta_description, pd.meta_keyword
                FROM `". DB_PREFIX . "product_description` pd
                WHERE pd.product_id IN ({$productIdsList})
                AND pd.language_id IN ({$languageIdsList})";

        $query = $this->db->query($sql);
        
        $descriptions = [];
        foreach ($query->rows as $row) {
            $descriptions[$row['product_id']][$row['language_id']] = $row;
        }

        return $descriptions;
    }

    /**
     * Извлича категории за множество продукти с една заявка
     * 
     * @param array $productIds Масив от product_id
     * @return array Асоциативен масив [product_id => [category_data]]
     */
    public function getProductCategories($productIds) {
        if (empty($productIds)) {
            return [];
        }

        $productIdsList = implode(',', array_map('intval', $productIds));

        $sql = "SELECT p2c.product_id, c.category_id, cd.name as category_name, c.parent_id,
                       GROUP_CONCAT(cd_path.name ORDER BY cp.level SEPARATOR ' > ') as category_path
                FROM `". DB_PREFIX . "product_to_category` p2c
                LEFT JOIN `". DB_PREFIX . "category` c ON (p2c.category_id = c.category_id)
                LEFT JOIN `". DB_PREFIX . "category_description` cd ON (c.category_id = cd.category_id AND cd.language_id = '{$this->languageId}')
                LEFT JOIN `". DB_PREFIX . "category_path` cp ON (c.category_id = cp.category_id)
                LEFT JOIN `". DB_PREFIX . "category_description` cd_path ON (cp.path_id = cd_path.category_id AND cd_path.language_id = '{$this->languageId}')
                WHERE p2c.product_id IN ({$productIdsList})
                GROUP BY p2c.product_id, c.category_id
                ORDER BY p2c.product_id, category_path";

        $query = $this->db->query($sql);
        
        $categories = [];
        foreach ($query->rows as $row) {
            $categories[$row['product_id']][] = $row;
        }
        return $categories;
    }

    /**
     * Извлича опции за множество продукти с една заявка
     *
     * @param array $productIds Масив от product_id
     * @return array Асоциативен масив [product_id => [option_data]]
     */
    public function getProductOptions($productIds) {
        if (empty($productIds)) {
            return [];
        }

        $productIdsList = implode(',', array_map('intval', $productIds));

        $sql = "SELECT po.product_id, po.option_id, od.name as option_name,
                       pov.option_value_id, ovd.name as value, po.required
                FROM `". DB_PREFIX . "product_option` po
                LEFT JOIN `". DB_PREFIX . "option_description` od ON (po.option_id = od.option_id AND od.language_id = '{$this->languageId}')
                LEFT JOIN `". DB_PREFIX . "product_option_value` pov ON (po.product_option_id = pov.product_option_id)
                LEFT JOIN `". DB_PREFIX . "option_value_description` ovd ON (pov.option_value_id = ovd.option_value_id AND ovd.language_id = '{$this->languageId}')
                WHERE po.product_id IN ({$productIdsList})
                ORDER BY po.product_id, od.name, ovd.name";

        $query = $this->db->query($sql);

        $options = [];
        foreach ($query->rows as $row) {
            // Ако няма стойност на опцията, пропускаме записа
            if (empty($row['value'])) {
                continue;
            }

            $options[$row['product_id']][] = [
                'option_id' => $row['option_id'],
                'option_name' => $row['option_name'],
                'value' => $row['value'],
                'required' => $row['required']
            ];
        }

        return $options;
    }

    /**
     * Извлича атрибути за множество продукти с една заявка
     * 
     * @param array $productIds Масив от product_id
     * @return array Асоциативен масив [product_id => [attribute_data]]
     */
    public function getProductAttributes($productIds) {
        if (empty($productIds)) {
            return [];
        }

        $productIdsList = implode(',', array_map('intval', $productIds));

        $sql = "SELECT pa.product_id, pa.attribute_id, ad.name as attribute_name, pa.text as attribute_text
                FROM `". DB_PREFIX . "product_attribute` pa
                LEFT JOIN `". DB_PREFIX . "attribute_description` ad ON (pa.attribute_id = ad.attribute_id AND ad.language_id = '{$this->languageId}')
                WHERE pa.product_id IN ({$productIdsList})
                AND pa.language_id = '{$this->languageId}'
                ORDER BY pa.product_id, ad.name";

        $query = $this->db->query($sql);
        
        $attributes = [];
        foreach ($query->rows as $row) {
            $attributes[$row['product_id']][] = $row;
        }

        return $attributes;
    }

    /**
     * Извлича всички подкатегории за дадени категории (рекурсивно)
     * 
     * @param array $categoryIds Масив от category_id
     * @return array Масив с всички category_id включително подкатегории
     */
    private function getAllSubcategoryIds($categoryIds) {
        $allIds = $categoryIds;
        $processedIds = [];

        while (!empty($categoryIds)) {
            $currentBatch = array_diff($categoryIds, $processedIds);
            if (empty($currentBatch)) {
                break;
            }

            $categoryIdsList = implode(',', array_map('intval', $currentBatch));
            
            $sql = "SELECT category_id FROM `". DB_PREFIX . "category` 
                    WHERE parent_id IN ({$categoryIdsList})";
            
            $query = $this->db->query($sql);
            
            $newIds = [];
            foreach ($query->rows as $row) {
                $newIds[] = $row['category_id'];
                if (!in_array($row['category_id'], $allIds)) {
                    $allIds[] = $row['category_id'];
                }
            }

            $processedIds = array_merge($processedIds, $currentBatch);
            $categoryIds = $newIds;
        }

        return array_unique($allIds);
    }

    // getLanguageId() метод премахнат - използваме setLanguageId() от контролера

    /**
     * Извлича допълнителни изображения за множество продукти с една заявка
     *
     * @param array $productIds Масив от product_id
     * @return array Асоциативен масив [product_id => [image_urls]]
     */
    public function getAdditionalImages($productIds) {
        if (empty($productIds)) {
            return [];
        }

        $productIdsList = implode(',', array_map('intval', $productIds));

        $sql = "SELECT product_id, image
                FROM `". DB_PREFIX . "product_image`
                WHERE product_id IN ({$productIdsList})
                ORDER BY product_id, sort_order";

        $query = $this->db->query($sql);

        $images = [];
        foreach ($query->rows as $row) {
            if (!empty($row['image'])) {
                $images[$row['product_id']][] = $this->getImageUrl($row['image']);
            }
        }

        return $images;
    }

    /**
     * Генерира URL за изображение
     *
     * @param string $imagePath Път към изображението
     * @return string URL на изображението
     */
    public function getImageUrl($imagePath) {
        if (empty($imagePath)) {
            return '';
        }

        // Използваме ThemeData за получаване на правилния URL
        if (function_exists('ThemeData') && ThemeData()) {
            return ThemeData()->getImageServerUrl() . $imagePath;
        }

        // Fallback
        return HTTP_CATALOG . 'image/' . $imagePath;
    }

    /**
     * Извлича пътеки на категории за множество категории с една заявка
     * Използва category_path таблицата за по-ефективно извличане на пълните пътеки
     *
     * @param array $categoryIds Масив от category_id
     * @return array Асоциативен масив [category_id => path_array]
     */
    public function getCategoryPaths($categoryIds) {
        if (empty($categoryIds)) {
            return [];
        }

        $categoryIdsList = implode(',', array_map('intval', $categoryIds));

        $sql = "SELECT
                    cp.category_id,
                    GROUP_CONCAT(cd.name ORDER BY cp.level SEPARATOR ' > ') AS path
                FROM `". DB_PREFIX . "category_path` cp
                LEFT JOIN `". DB_PREFIX . "category_description` cd ON (cp.path_id = cd.category_id AND cd.language_id = '{$this->languageId}')
                WHERE cp.category_id IN ({$categoryIdsList})
                GROUP BY cp.category_id
                ORDER BY cp.category_id";

        $query = $this->db->query($sql);

        $paths = [];
        foreach ($query->rows as $row) {
            // Разделяме пътя на масив от имена на категории
            $pathArray = explode(' > ', $row['path']);
            $paths[$row['category_id']] = $pathArray;
        }

        return $paths;
    }

    /**
     * Извлича промоционални цени за множество продукти с оптимизирана заявка
     *
     * @param array $productIds Масив от product_id
     * @param array $productPrices Асоциативен масив [product_id => original_price]
     * @return array Асоциативен масив [product_id => promo_price]
     */
    public function getPromotionalPricesOptimized($productIds, $productPrices) {
        if (empty($productIds)) {
            return [];
        }

        $productIdsList = implode(',', array_map('intval', $productIds));
        $currentDate = date('Y-m-d');

        $sql = "SELECT ps.product_id, ps.price as promo_price, ps.priority
                FROM `". DB_PREFIX . "product_special` ps
                WHERE ps.product_id IN ({$productIdsList})
                AND ps.customer_group_id = '1'
                AND (ps.date_start = '0000-00-00' OR ps.date_start <= '{$currentDate}')
                AND (ps.date_end = '0000-00-00' OR ps.date_end >= '{$currentDate}')
                ORDER BY ps.product_id, ps.priority ASC, ps.price ASC";

        $query = $this->db->query($sql);

        $promoPrices = [];
        foreach ($query->rows as $row) {
            $productId = $row['product_id'];
            $promoPrice = (float)$row['promo_price'];
            $originalPrice = (float)($productPrices[$productId] ?? 0);

            // Взимаме най-ниската активна промо цена която е по-ниска от оригиналната
            if ($promoPrice < $originalPrice && (!isset($promoPrices[$productId]) || $promoPrice < $promoPrices[$productId])) {
                $promoPrices[$productId] = $promoPrice;
            }
        }

        return $promoPrices;
    }

    /**
     * Обработва batch от продукти за експорт с оптимизирано извличане на данни
     *
     * @param array $productIds Масив от product_id
     * @param array $options Опции за експорта
     * @return array Пълни данни за продуктите
     */
    public function getProductsForExport($productIds, $options = []) {
        if (empty($productIds)) {
            return [];
        }

        // Обработваме в batch-ове за оптимизация
        $batches = array_chunk($productIds, $this->batchSize);
        $allProducts = [];
        $batchCount = count($batches);

        foreach ($batches as $batchIndex => $batch) {
            // Логиране на прогреса
            if (function_exists('isDeveloper') && isDeveloper()) {
                error_log("Обработка на batch " . ($batchIndex + 1) . " от {$batchCount} (" . count($batch) . " продукта)");
            }

            // Основни продуктови данни
            $products = $this->getProductsByIds($batch);

            if (empty($products)) {
                continue;
            }

            // Подготвяме масив с оригинални цени за промо цените
            $productPrices = [];
            foreach ($products as $product) {
                $productPrices[$product['product_id']] = (float)($product['price'] ?? 0);
            }

            // Извличаме всички необходими данни с групирани заявки
            $promoPrices = [];
            $categories = [];
            $productOptions = [];
            $attributes = [];
            $additionalImages = [];
            $categoryPaths = [];
            $descriptions = [];

            // Промоционални цени ако са нужни
            if (!empty($options['include_promo_price'])) {
                $promoPrices = $this->getPromotionalPricesOptimized($batch, $productPrices);
            }

            // Категории ако са нужни
            if (!empty($options['include_categories'])) {
                $categories = $this->getProductCategories($batch);

                // Извличаме пътеките на категориите
                $allCategoryIds = [];
                foreach ($categories as $productCategories) {
                    foreach ($productCategories as $category) {
                        $allCategoryIds[] = $category['category_id'];
                    }
                }
                if (!empty($allCategoryIds)) {
                    $categoryPaths = $this->getCategoryPaths(array_unique($allCategoryIds));
                }
            }

            // Опции ако са нужни
            if (!empty($options['include_options'])) {
                $productOptions = $this->getProductOptions($batch);
            }

            // Атрибути ако са нужни
            if (!empty($options['include_attributes'])) {
                $attributes = $this->getProductAttributes($batch);
            }

            // Допълнителни изображения ако са нужни
            if (!empty($options['include_additional_images'])) {
                $additionalImages = $this->getAdditionalImages($batch);
            }

            // Многоезични описания ако са нужни
            // if (!empty($options['include_descriptions'])) {
                $descriptions = $this->getProductDescriptions($batch);
            // }

            // Комбинираме данните
            foreach ($products as &$product) {
                $productId = $product['product_id'];

                // Добавяме промо цена
                $product['promo_price'] = $promoPrices[$productId] ?? 0.00;

                // Добавяме категории с пътеки
                $productCategories = $categories[$productId] ?? [];
                foreach ($productCategories as &$category) {
                    $category['path'] = $categoryPaths[$category['category_id']] ?? [$category['category_name']];
                }
                $product['product_categories'] = $productCategories;

                // Добавяме опции
                $product['product_options'] = $productOptions[$productId] ?? [];

                // Добавяме атрибути
                $product['product_attributes'] = $attributes[$productId] ?? [];

                // Добавяме допълнителни изображения
                $product['additional_images'] = $additionalImages[$productId] ?? [];

                // Обработваме главното изображение
                if (!empty($product['image'])) {
                    $product['image_url'] = $this->getImageUrl($product['image']);
                } else {
                    $product['image_url'] = '';
                }

                // Добавяме многоезичните описания
                $product['product_descriptions'] = $descriptions[$productId] ?? [];
            }

            $allProducts = array_merge($allProducts, $products);

            // Memory management - освобождаваме паметта
            unset($products, $promoPrices, $categories, $productOptions, $attributes, $additionalImages, $categoryPaths, $descriptions);

            // Намалено изчакване между batch-овете за по-бърза обработка при големи експорти
            if ($batchIndex < $batchCount - 1) {
                usleep(500000); // 0.5 секунди вместо 1 секунда
            }

            // Подобрена проверка за memory limit
            $memoryLimit = $this->getMemoryLimitInBytes();
            $currentUsage = memory_get_usage(true);
            $memoryPercentage = ($currentUsage / $memoryLimit) * 100;

            if ($memoryPercentage > 75) {
                if (function_exists('isDeveloper') && isDeveloper()) {
                    $usageMB = round($currentUsage / 1024 / 1024, 2);
                    $limitMB = round($memoryLimit / 1024 / 1024, 2);
                    error_log("Предупреждение: Използването на памет достигна {$memoryPercentage}% ({$usageMB}MB от {$limitMB}MB) в batch {$batchIndex}");
                }

                // Принудително почистване на паметта
                gc_collect_cycles();

                // Ако паметта е над 85%, хвърляме грешка за превенция на crash
                if ($memoryPercentage > 85) {
                    throw new \Exception("Критично ниво на памет достигнато: {$memoryPercentage}%. Експортът е прекратен за превенция на crash.");
                }
            }
        }

        return $allProducts;
    }

    /**
     * Получава memory limit в байтове
     *
     * @return int Memory limit в байтове
     */
    private function getMemoryLimitInBytes() {
        $memoryLimit = ini_get('memory_limit');

        if ($memoryLimit == -1) {
            return PHP_INT_MAX; // Неограничена памет
        }

        $unit = strtolower(substr($memoryLimit, -1));
        $value = (int)$memoryLimit;

        switch ($unit) {
            case 'g':
                $value *= 1024 * 1024 * 1024;
                break;
            case 'm':
                $value *= 1024 * 1024;
                break;
            case 'k':
                $value *= 1024;
                break;
        }

        return $value;
    }

    /**
     * Получава ID-тата на всички продукти (активни и неактивни)
     *
     * @param bool $includeInactive Дали да включи неактивните продукти (по подразбиране true)
     * @return array Масив от product_id
     */
    public function getAllProductIds($includeInactive = true) {
        $whereClause = $includeInactive ? '' : "WHERE 1 = 1";

        $sql = "SELECT product_id
                FROM `". DB_PREFIX . "product`
                {$whereClause}
                ORDER BY product_id";

        $query = $this->db->query($sql);

        return array_column($query->rows, 'product_id');
    }

    /**
     * Получава продуктите от дадена категория (включително подкатегории)
     *
     * @param int $categoryId ID на категорията
     * @param bool $includeInactive Дали да включи неактивните продукти
     * @return array Масив от product_id
     */
    public function getProductsFromCategory($categoryId, $includeInactive = true) {
        // Получаваме всички подкатегории рекурсивно
        $allCategoryIds = $this->getAllSubcategories($categoryId);

        if (empty($allCategoryIds)) {
            return [];
        }

        $categoryIdsStr = implode(',', array_map('intval', $allCategoryIds));

        $sql = "SELECT DISTINCT p.product_id
                FROM `". DB_PREFIX . "product` p
                LEFT JOIN `". DB_PREFIX . "product_to_category` ptc ON (p.product_id = ptc.product_id)
                WHERE ptc.category_id IN ({$categoryIdsStr})
                ORDER BY p.product_id";

        $query = $this->db->query($sql);

        return array_column($query->rows, 'product_id');
    }

    /**
     * Получава всички подкатегории на дадена категория рекурсивно
     *
     * @param int $categoryId ID на категорията
     * @return array Масив от category_id включително основната категория
     */
    public function getAllSubcategories($categoryId) {
        $categories = [$categoryId]; // Включваме основната категория

        // Получаваме директните подкатегории
        $sql = "SELECT category_id
                FROM `". DB_PREFIX . "category`
                WHERE parent_id = '{$categoryId}'";

        $result = $this->db->query($sql);

        foreach ($result->rows as $row) {
            // Рекурсивно получаваме подкатегориите на всяка подкатегория
            $subcategories = $this->getAllSubcategories($row['category_id']);
            $categories = array_merge($categories, $subcategories);
        }

        // Премахваме дублиращи се ID-та и връщаме уникални стойности
        return array_unique($categories);
    }

    /**
     * Почиства текста от HTML тагове и специални символи
     *
     * @param string $text Текст за почистване
     * @return string Почистен текст
     */
    public function cleanText($text) {
        // Премахваме HTML тагове
        $text = strip_tags($text);

        // Декодираме HTML entities
        $text = html_entity_decode($text, ENT_QUOTES, 'UTF-8');

        // Премахваме излишни интервали
        $text = trim(preg_replace('/\s+/', ' ', $text));

        return $text;
    }

    /**
     * Форматира цена като float с 2 десетични знака
     *
     * @param mixed $price Цена за форматиране
     * @return string Форматирана цена
     */
    public function formatPrice($price) {
        return number_format((float)$price, 2, '.', '');
    }

    /**
     * Форматира количество като integer
     *
     * @param mixed $quantity Количество за форматиране
     * @return int Форматирано количество
     */
    public function formatQuantity($quantity) {
        return (int)$quantity;
    }

    /**
     * Извлича статистики за експорта
     *
     * @param array $productIds Масив от product_id
     * @return array Статистики
     */
    public function getExportStatistics($productIds) {
        if (empty($productIds)) {
            return [
                'total_products' => 0,
                'estimated_batches' => 0,
                'estimated_time' => 0
            ];
        }

        $totalProducts = count($productIds);
        $estimatedBatches = ceil($totalProducts / $this->batchSize);
        $estimatedTime = $estimatedBatches * 2; // 2 секунди на batch (1 сек обработка + 1 сек изчакване)

        return [
            'total_products' => $totalProducts,
            'estimated_batches' => $estimatedBatches,
            'estimated_time' => $estimatedTime,
            'batch_size' => $this->batchSize
        ];
    }

    /**
     * Проверява наличната памет и връща препоръки
     *
     * @return array Информация за паметта
     */
    public function getMemoryInfo() {
        $memoryLimit = ini_get('memory_limit');
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);

        // Конвертираме memory_limit в байтове
        $memoryLimitBytes = $this->convertToBytes($memoryLimit);

        return [
            'memory_limit' => $memoryLimit,
            'memory_limit_bytes' => $memoryLimitBytes,
            'current_usage' => $memoryUsage,
            'peak_usage' => $memoryPeak,
            'usage_percentage' => round(($memoryUsage / $memoryLimitBytes) * 100, 2),
            'recommended_batch_size' => $this->calculateOptimalBatchSize($memoryLimitBytes, $memoryUsage)
        ];
    }

    /**
     * Конвертира memory_limit стойност в байтове
     *
     * @param string $value Стойност като '128M', '1G', etc.
     * @return int Байтове
     */
    private function convertToBytes($value) {
        $value = trim($value);
        $last = strtolower($value[strlen($value)-1]);
        $value = (int)$value;

        switch($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    /**
     * Изчислява оптимален размер на batch според наличната памет
     *
     * @param int $memoryLimitBytes Лимит на паметта в байтове
     * @param int $currentUsage Текущо използване на паметта
     * @return int Препоръчителен размер на batch
     */
    private function calculateOptimalBatchSize($memoryLimitBytes, $currentUsage) {
        $availableMemory = $memoryLimitBytes - $currentUsage;
        $safeMemory = $availableMemory * 0.6; // Използваме 60% от наличната памет

        // Приблизително 50KB на продукт с всички данни
        $memoryPerProduct = 50 * 1024;
        $optimalBatchSize = floor($safeMemory / $memoryPerProduct);

        // Минимум 10, максимум 200
        return max(10, min(200, $optimalBatchSize));
    }
}

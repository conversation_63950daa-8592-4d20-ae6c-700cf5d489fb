# Debug Guide за Export функционалността

## Промени направени за диагностика

### 1. Намален праг за големи експорти
- **Преди**: 1000 продукта
- **Сега**: 500 продукта
- **Цел**: По-ранно задействане на batch режим

### 2. Намален memory threshold
- **Преди**: 75% warning, 85% critical
- **Сега**: 65% warning, 75% critical
- **Цел**: По-ранно предупреждение за memory проблеми

### 3. Добавен fallback механизъм
- При memory грешка в директен експорт → автоматично превключване към batch режим
- Проверка за думите "памет" или "memory" в грешката

### 4. Подробно debug логване

#### В контролера (Export.php):
```php
// Логване на броя продукти и прага
F()->log->developer("Export Debug: Общо продукти за експорт: {$productCount}", __FILE__, __LINE__);
F()->log->developer("Export Debug: Праг за големи експорти: {$this->largeExportThreshold}", __FILE__, __LINE__);

// Логване на избраните критерии
F()->log->developer("Export Debug: Избрани категории: " . count($this->selectedCategories), __FILE__, __LINE__);
F()->log->developer("Export Debug: Избрани продукти: " . count($this->selectedProducts), __FILE__, __LINE__);

// Логване на режима на експорт
F()->log->developer("Export Debug: Задействане на голям експорт режим за {$productCount} продукта", __FILE__, __LINE__);
```

#### В JavaScript (product-export.js):
```javascript
// Логване на JSON response
console.log('Export Debug: Получен JSON response:', data);

// Логване на large export режим
console.log('Export Debug: Задействане на large export режим:', data);

// Логване на progress bar
console.log('Progress Bar Debug: Показване на progress bar', exportData);
```

## Как да тествате

### 1. Отворете Developer Tools
- Натиснете F12 в браузера
- Отидете на Console таба

### 2. Проверете debug логовете
- В PHP логовете търсете "Export Debug" и "Large Export Debug"
- В браузера търсете "Export Debug" и "Progress Bar Debug"

### 3. Тестови сценарии

#### Тест 1: Малък експорт (под 500 продукта)
1. Изберете няколко категории или продукта (общо под 500)
2. Започнете експорт
3. **Очакван резултат**: Директен експорт без progress bar

#### Тест 2: Голям експорт (над 500 продукта)
1. Не избирайте нищо (всички продукти) или изберете много категории
2. Започнете експорт
3. **Очакван резултат**: Progress bar интерфейс

#### Тест 3: Fallback механизъм
1. Ако получите memory грешка при директен експорт
2. **Очакван резултат**: Автоматично превключване към batch режим

## Debug съобщения за търсене

### В PHP логовете:
- `Export Debug: Общо продукти за експорт:`
- `Export Debug: Задействане на голям експорт режим`
- `Large Export Debug: Започване на голям експорт`
- `getProductsForExport Debug: getAllProductIds върна`

### В браузера (Console):
- `Export Debug: Получен JSON response:`
- `Export Debug: Задействане на large export режим:`
- `Progress Bar Debug: Показване на progress bar`
- `Progress Bar Debug: Progress bar е добавен към DOM`

## Възможни проблеми и решения

### 1. Не се задейства large export режим
**Симптоми**: Винаги се опитва директен експорт
**Проверка**: Търсете в логовете броя продукти и дали е над 500
**Решение**: Проверете дали `getAllProductIds()` връща правилния брой

### 2. JSON response не се обработва правилно
**Симптоми**: Не се показва progress bar
**Проверка**: Търсете в Console "Export Debug: Получен JSON response"
**Решение**: Проверете дали response-ът съдържа `large_export: true`

### 3. Progress bar не се показва
**Симптоми**: JavaScript получава правилния response но няма UI
**Проверка**: Търсете "Progress Bar Debug" съобщения
**Решение**: Проверете дали DOM елементите се създават правилно

### 4. Memory грешка въпреки batch режим
**Симптоми**: Грешка "Критично ниво на памет достигнато"
**Проверка**: Проверете дали се използва по-малкия batch размер (75)
**Решение**: Намалете още batch размера или увеличете PHP memory_limit

## Файлове за проверка на логове

### PHP логове:
- Основен лог файл на системата
- Developer логове (ако е включен developer режим)

### Browser Console:
- F12 → Console таб
- Филтрирайте по "Export Debug" или "Progress Bar Debug"

## Следващи стъпки

1. **Тествайте с различни брой продукти** (под и над 500)
2. **Проверете логовете** за всеки тест
3. **Докладвайте резултатите** с конкретни debug съобщения
4. **Ако проблемът продължава**, ще добавим още debug информация

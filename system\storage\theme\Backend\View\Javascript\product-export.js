/**
 * JavaScript функционалност за експорт на продукти
 * Интегриран с BackendModule архитектурата
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        // Инициализиране на функционалността за експорт на продукти
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initProductExportModule();
        }
    });

    // Добавяне на функционалност за експорт на продукти към основния модул
    if (typeof BackendModule !== 'undefined') {
        // Ensure config object exists
        BackendModule.config = BackendModule.config || {};

        // Extract user_token from URL and store it
        try {
            const params = new URLSearchParams(window.location.search);
            BackendModule.config.userToken = params.get('user_token') || '';
        } catch (e) {
            BackendModule.config.userToken = '';
        }

        // Добавяне на методи към основния модул
        Object.assign(BackendModule, {
            /**
             * Инициализация на функционалността за експорт на продукти
             */
            initProductExportModule: function() {
                this.productExportBindEvents();
                this.productExportInitializeAutocomplete();
                this.productExportInitializeExportForm();
            },

            /**
             * Свързване на събития за експорт на продукти
             */
            productExportBindEvents: function() {
                // Форма за експорт
                const exportForm = document.getElementById('export-form');
                if (exportForm) {
                    exportForm.addEventListener('submit', (e) => this.productExportHandleExportSubmit(e));
                }

                // Главен бутон за експорт
                const exportBtn = document.getElementById('export-submit-btn');
                if (exportBtn) {
                    exportBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.productExportHandleExportSubmit(e);
                    });
                }

                // Бутони за изчистване на избрани елементи
                const clearCategoriesBtn = document.getElementById('clear-categories');
                const clearProductsBtn = document.getElementById('clear-products');

                if (clearCategoriesBtn) {
                    clearCategoriesBtn.addEventListener('click', () => this.productExportClearCategories());
                }
                if (clearProductsBtn) {
                    clearProductsBtn.addEventListener('click', () => this.productExportClearProducts());
                }

                // Toggle за field selector
                const toggleFieldSelector = document.getElementById('toggle-field-selector');
                if (toggleFieldSelector) {
                    toggleFieldSelector.addEventListener('click', () => this.productExportToggleFieldSelector());
                }
            },

            /**
             * Инициализация на автозавършване за експорт на продукти
             */
            productExportInitializeAutocomplete: function() {
                this.productExportInitializeCategoryAutocomplete();
                this.productExportInitializeProductAutocomplete();
            },

            /**
             * Автозавършване за категории (множествен избор)
             */
            productExportInitializeCategoryAutocomplete: function() {
                const categoryInput = document.getElementById('category-autocomplete');
                const categoryResults = document.getElementById('category-results');
                const selectedCategoriesContainer = document.getElementById('selected-categories');

                if (!categoryInput || !categoryResults || !selectedCategoriesContainer) {
                    return;
                }

                let debounceTimer;
                let currentController;

                categoryInput.addEventListener('input', (e) => {
                    clearTimeout(debounceTimer);

                    if (currentController) {
                        currentController.abort();
                    }

                    debounceTimer = setTimeout(() => {
                        this.productExportSearchCategories(e.target.value, categoryResults);
                    }, 500); // debounceDelay
                });

                categoryInput.addEventListener('focus', () => {
                    if (categoryInput.value.trim() === '') {
                        this.productExportSearchCategories('', categoryResults);
                    }
                });

                // Скриване на резултатите при клик извън тях
                document.addEventListener('click', (e) => {
                    if (!categoryInput.contains(e.target) && !categoryResults.contains(e.target)) {
                        categoryResults.classList.add('hidden');
                    }
                });
            },

            /**
             * Автозавършване за продукти (множествен избор)
             */
            productExportInitializeProductAutocomplete: function() {
                const productInput = document.getElementById('product-autocomplete');
                const productResults = document.getElementById('product-results');
                const selectedProductsContainer = document.getElementById('selected-products');

                if (!productInput || !productResults || !selectedProductsContainer) {
                    return;
                }

                let debounceTimer;
                let currentController;

                productInput.addEventListener('input', (e) => {
                    clearTimeout(debounceTimer);

                    if (currentController) {
                        currentController.abort();
                    }

                    debounceTimer = setTimeout(() => {
                        this.productExportSearchProducts(e.target.value, productResults);
                    }, 500); // debounceDelay
                });

                productInput.addEventListener('focus', () => {
                    if (productInput.value.trim() === '') {
                        this.productExportSearchProducts('', productResults);
                    }
                });

                // Скриване на резултатите при клик извън тях
                document.addEventListener('click', (e) => {
                    if (!productInput.contains(e.target) && !productResults.contains(e.target)) {
                        productResults.classList.add('hidden');
                    }
                });
            },

            /**
             * Търсене на категории за експорт
             */
            productExportSearchCategories: function(query, resultsContainer) {
                // Инициализиране на състоянието ако не съществува
                if (!this.productExportState) {
                    this.productExportState = {
                        activeRequests: new Set(),
                        requestQueue: [],
                        selectedCategories: new Map(),
                        selectedProducts: new Map()
                    };
                }

                if (this.productExportState.activeRequests.size >= 10) { // maxConcurrentRequests
                    this.productExportState.requestQueue.push(() => this.productExportSearchCategories(query, resultsContainer));
                    return;
                }

                const controller = new AbortController();
                this.productExportState.activeRequests.add(controller);

                const userToken = this.getUserToken();
                const timestamp = Date.now();
                const currentQuery = query.trim();
                const limit = currentQuery === '' ? 10 : 1000;

                let fetchUrl = `index.php?route=catalog/product/export/autocompleteCategory&filter_name=${encodeURIComponent(currentQuery)}&limit=${limit}&user_token=${userToken}&_=${timestamp}`;

                fetch(fetchUrl, {
                    signal: controller.signal,
                    cache: 'no-store',
                    headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                })
                .then(response => {
                    if (!response.ok) throw new Error('Грешка при зареждане на категории');
                    return response.json();
                })
                .then(data => {
                    this.productExportDisplayCategoryResults(data, resultsContainer);
                })
                .catch(error => {
                    if (error.name !== 'AbortError') {
                        this.productExportShowError('Грешка при зареждане на категории');
                    }
                })
                .finally(() => {
                    this.productExportState.activeRequests.delete(controller);
                    this.productExportProcessQueue();
                });
            },

            /**
             * Търсене на продукти за експорт
             */
            productExportSearchProducts: function(query, resultsContainer) {
                // Инициализиране на състоянието ако не съществува
                if (!this.productExportState) {
                    this.productExportState = {
                        activeRequests: new Set(),
                        requestQueue: [],
                        selectedCategories: new Map(),
                        selectedProducts: new Map()
                    };
                }

                if (this.productExportState.activeRequests.size >= 10) { // maxConcurrentRequests
                    this.productExportState.requestQueue.push(() => this.productExportSearchProducts(query, resultsContainer));
                    return;
                }

                const controller = new AbortController();
                this.productExportState.activeRequests.add(controller);

                const userToken = this.getUserToken();
                const timestamp = Date.now();
                const currentQuery = query.trim();
                const limit = currentQuery === '' ? 10 : 1000;

                let fetchUrl = `index.php?route=catalog/product/export/autocompleteProduct&filter_name=${encodeURIComponent(currentQuery)}&limit=${limit}&user_token=${userToken}&_=${timestamp}`;

                fetch(fetchUrl, {
                    signal: controller.signal,
                    cache: 'no-store',
                    headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                })
                .then(response => {
                    if (!response.ok) throw new Error('Грешка при зареждане на продукти');
                    return response.json();
                })
                .then(data => {
                    this.productExportDisplayProductResults(data, resultsContainer);
                })
                .catch(error => {
                    if (error.name !== 'AbortError') {
                        this.productExportShowError('Грешка при зареждане на продукти');
                    }
                })
                .finally(() => {
                    this.productExportState.activeRequests.delete(controller);
                    this.productExportProcessQueue();
                });
            },

            /**
             * Показване на резултати за категории
             */
            productExportDisplayCategoryResults: function(data, container) {
                container.innerHTML = '';

                if (!Array.isArray(data) || data.length === 0) {
                    const noResults = document.createElement('div');
                    noResults.className = 'p-2 text-gray-500 autocomplete-no-results';
                    noResults.textContent = 'Няма намерени категории';
                    container.appendChild(noResults);
                } else {
                    data.forEach(category => {
                        const item = document.createElement('div');
                        item.className = 'p-2 cursor-pointer hover:bg-gray-100 autocomplete-suggestion';
                        item.textContent = category.name;
                        item.dataset.id = category.category_id;

                        item.addEventListener('click', () => {
                            this.productExportAddCategory({
                                id: category.category_id,
                                name: category.name
                            });
                            container.classList.add('hidden');
                            document.getElementById('category-autocomplete').value = '';
                        });

                        container.appendChild(item);
                    });
                }

                container.classList.remove('hidden');
            },

            /**
             * Показване на резултати за продукти
             */
            productExportDisplayProductResults: function(data, container) {
                container.innerHTML = '';

                if (!Array.isArray(data) || data.length === 0) {
                    const noResults = document.createElement('div');
                    noResults.className = 'p-2 text-sm text-red-500';
                    noResults.textContent = 'Няма намерени продукти';
                    container.appendChild(noResults);
                } else {
                    let hasVisibleProducts = false;

                    data.forEach(product => {
                        // Проверяваме дали продуктът вече е избран
                        const isSelected = this.productExportState.selectedProducts.has(product.product_id);

                        if (!isSelected) {
                            hasVisibleProducts = true;
                            const item = document.createElement('div');
                            item.className = 'p-2 cursor-pointer hover:bg-gray-100 flex items-center space-x-2';

                            item.innerHTML = `
                                <img src="${product.thumb}" alt="${this.productExportEscapeHtml(product.name)}" class="w-8 h-8 object-cover rounded">
                                <div class="flex-1">
                                    <div class="text-sm font-medium">${this.productExportEscapeHtml(product.name)}</div>
                                    <div class="text-xs text-gray-500">${this.productExportEscapeHtml(product.model)}</div>
                                </div>
                            `;

                            item.addEventListener('click', () => {
                                this.productExportAddProduct(product);
                                container.classList.add('hidden');
                                document.getElementById('product-autocomplete').value = '';
                            });

                            container.appendChild(item);
                        }
                    });

                    if (!hasVisibleProducts) {
                        const noResults = document.createElement('div');
                        noResults.className = 'p-2 text-sm text-gray-500';
                        noResults.textContent = 'Всички намерени продукти са вече избрани';
                        container.appendChild(noResults);
                    }
                }

                container.classList.remove('hidden');
            },

            /**
             * Добавяне на категория за експорт
             */
            productExportAddCategory: function(category) {
                // Инициализиране на състоянието ако не съществува
                if (!this.productExportState) {
                    this.productExportState = {
                        activeRequests: new Set(),
                        requestQueue: [],
                        selectedCategories: new Map(),
                        selectedProducts: new Map()
                    };
                }

                if (this.productExportState.selectedCategories.has(category.id)) {
                    this.productExportShowError('Тази категория вече е избрана');
                    return;
                }

                this.productExportState.selectedCategories.set(category.id, category);
                this.productExportUpdateSelectedCategoriesDisplay();
            },

            /**
             * Добавяне на продукт за експорт
             */
            productExportAddProduct: function(product) {
                // Инициализиране на състоянието ако не съществува
                if (!this.productExportState) {
                    this.productExportState = {
                        activeRequests: new Set(),
                        requestQueue: [],
                        selectedCategories: new Map(),
                        selectedProducts: new Map()
                    };
                }

                if (this.productExportState.selectedProducts.has(product.product_id)) {
                    this.productExportShowError('Този продукт вече е избран');
                    return;
                }

                this.productExportState.selectedProducts.set(product.product_id, product);
                this.productExportUpdateSelectedProductsDisplay();
            },

            /**
             * Обновяване на показването на избраните категории
             */
            productExportUpdateSelectedCategoriesDisplay: function() {
                const container = document.getElementById('selected-categories');
                if (!container) return;

                container.innerHTML = '';

                this.productExportState.selectedCategories.forEach((category, id) => {
                    const item = document.createElement('div');
                    item.className = 'flex items-center space-x-2 p-2 bg-blue-50 rounded border border-blue-200 mb-2';
                    item.innerHTML = `
                        <div class="flex-1">
                            <div class="text-sm font-medium text-blue-800">${this.productExportEscapeHtml(category.name)}</div>
                            <div class="text-xs text-blue-600">Избрана категория</div>
                        </div>
                        <button type="button" class="text-blue-500 hover:text-blue-700 p-1" data-id="${id}">
                            <i class="ri-close-line"></i>
                        </button>
                    `;

                    const removeBtn = item.querySelector('button');
                    removeBtn.addEventListener('click', () => {
                        this.productExportState.selectedCategories.delete(id);
                        this.productExportUpdateSelectedCategoriesDisplay();
                    });

                    container.appendChild(item);
                });

                this.productExportUpdateCategoryCount();
            },

            /**
             * Обновяване на показването на избраните продукти
             */
            productExportUpdateSelectedProductsDisplay: function() {
                const container = document.getElementById('selected-products');
                if (!container) return;

                container.innerHTML = '';

                this.productExportState.selectedProducts.forEach((product, id) => {
                    const item = document.createElement('div');
                    item.className = 'flex items-center space-x-2 p-2 bg-green-50 rounded border border-green-200 mb-2';
                    item.innerHTML = `
                        <img src="${product.thumb || ''}" alt="${this.productExportEscapeHtml(product.name)}" class="w-8 h-8 object-cover rounded">
                        <div class="flex-1">
                            <div class="text-sm font-medium text-green-800">${this.productExportEscapeHtml(product.name)}</div>
                            <div class="text-xs text-green-600">Модел: ${this.productExportEscapeHtml(product.model)}</div>
                        </div>
                        <button type="button" class="text-green-500 hover:text-green-700 p-1" data-id="${id}">
                            <i class="ri-close-line"></i>
                        </button>
                    `;

                    const removeBtn = item.querySelector('button');
                    removeBtn.addEventListener('click', () => {
                        this.productExportState.selectedProducts.delete(id);
                        this.productExportUpdateSelectedProductsDisplay();
                    });

                    container.appendChild(item);
                });

                this.productExportUpdateProductCount();
            },

            /**
             * Обновяване на броя категории
             */
            productExportUpdateCategoryCount: function() {
                const countElement = document.getElementById('category-count');
                if (countElement) {
                    countElement.textContent = this.productExportState.selectedCategories.size;
                }
            },

            /**
             * Обновяване на броя продукти
             */
            productExportUpdateProductCount: function() {
                const countElement = document.getElementById('product-count');
                if (countElement) {
                    countElement.textContent = this.productExportState.selectedProducts.size;
                }
            },

            /**
             * Изчистване на категориите
             */
            productExportClearCategories: function() {
                if (!this.productExportState) {
                    this.productExportState = {
                        activeRequests: new Set(),
                        requestQueue: [],
                        selectedCategories: new Map(),
                        selectedProducts: new Map()
                    };
                }
                this.productExportState.selectedCategories.clear();
                this.productExportUpdateSelectedCategoriesDisplay();
            },

            /**
             * Изчистване на продуктите
             */
            productExportClearProducts: function() {
                if (!this.productExportState) {
                    this.productExportState = {
                        activeRequests: new Set(),
                        requestQueue: [],
                        selectedCategories: new Map(),
                        selectedProducts: new Map()
                    };
                }
                this.productExportState.selectedProducts.clear();
                this.productExportUpdateSelectedProductsDisplay();
            },

            /**
             * Инициализация на формата за експорт
             */
            productExportInitializeExportForm: function() {
                const formatRadios = document.querySelectorAll('input[name="export_format"]');
                formatRadios.forEach(radio => {
                    radio.addEventListener('change', () => {
                        this.productExportUpdateFormatInfo();
                        this.productExportSaveSettings();
                    });
                });

                // Добавяне на event listeners за основните опции
                const basicOptions = [
                    'include_descriptions',
                    'include_categories',
                    'include_options'
                ];

                basicOptions.forEach(optionId => {
                    const element = document.getElementById(optionId);
                    if (element) {
                        element.addEventListener('change', () => this.productExportSaveSettings());
                    }
                });

                // Добавяне на event listeners за детайлните полета
                const fieldCheckboxes = document.querySelectorAll('.field-checkbox');
                fieldCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', () => this.productExportSaveSettings());
                });

                // Зареждане на запазените настройки
                this.productExportLoadSettings();
                this.productExportUpdateFormatInfo();
            },

            /**
             * Запазване на настройките за експорт в localStorage
             */
            productExportSaveSettings: function() {
                try {
                    const settings = {
                        format: document.querySelector('input[name="export_format"]:checked')?.value || 'csv',
                        includeDescriptions: document.getElementById('include_descriptions')?.checked || false,
                        includeCategories: document.getElementById('include_categories')?.checked || false,
                        includeOptions: document.getElementById('include_options')?.checked || false,
                        selectedFields: []
                    };

                    // Събиране на избраните детайлни полета
                    const fieldCheckboxes = document.querySelectorAll('.field-checkbox:checked');
                    fieldCheckboxes.forEach(checkbox => {
                        settings.selectedFields.push(checkbox.value);
                    });

                    // Запазване в localStorage
                    localStorage.setItem('productExportSettings', JSON.stringify(settings));

                    // Опционално: запазване и на сървъра чрез AJAX
                    this.productExportSaveSettingsToServer(settings);
                } catch (error) {
                    console.warn('Грешка при запазване на настройките за експорт:', error);
                }
            },

            /**
             * Зареждане на настройките за експорт от localStorage и сървъра
             */
            productExportLoadSettings: function() {
                try {
                    // Първо проверяваме за настройки от сървъра (ако са налични в window.savedSettings)
                    let settings = null;

                    if (window.savedSettings && typeof window.savedSettings === 'object') {
                        settings = window.savedSettings;
                    } else {
                        // Fallback към localStorage
                        const savedSettings = localStorage.getItem('productExportSettings');
                        if (savedSettings) {
                            settings = JSON.parse(savedSettings);
                        }
                    }

                    if (!settings) return;

                    // Задаване на формата
                    if (settings.format) {
                        const formatRadio = document.querySelector(`input[name="export_format"][value="${settings.format}"]`);
                        if (formatRadio) {
                            formatRadio.checked = true;
                        }
                    }

                    // Задаване на основните опции
                    if (settings.includeDescriptions !== undefined) {
                        const element = document.getElementById('include_descriptions');
                        if (element) element.checked = settings.includeDescriptions;
                    }

                    if (settings.includeCategories !== undefined) {
                        const element = document.getElementById('include_categories');
                        if (element) element.checked = settings.includeCategories;
                    }

                    if (settings.includeOptions !== undefined) {
                        const element = document.getElementById('include_options');
                        if (element) element.checked = settings.includeOptions;
                    }

                    // Задаване на детайлните полета
                    if (settings.selectedFields && Array.isArray(settings.selectedFields)) {
                        // Първо изчистваме всички
                        const allFieldCheckboxes = document.querySelectorAll('.field-checkbox');
                        allFieldCheckboxes.forEach(checkbox => {
                            checkbox.checked = false;
                        });

                        // После задаваме избраните
                        settings.selectedFields.forEach(fieldValue => {
                            const checkbox = document.querySelector(`.field-checkbox[value="${fieldValue}"]`);
                            if (checkbox) {
                                checkbox.checked = true;
                            }
                        });
                    }
                } catch (error) {
                    console.warn('Грешка при зареждане на настройките за експорт:', error);
                }
            },

            /**
             * Запазване на настройките на сървъра чрез AJAX
             */
            productExportSaveSettingsToServer: function(settings) {
                try {
                    const formData = new FormData();
                    formData.append('action', 'save_export_settings');
                    formData.append('settings', JSON.stringify(settings));
                    formData.append('user_token', this.config.userToken);

                    fetch(window.location.href, {
                        method: 'POST',
                        body: formData
                    }).catch(error => {
                        console.warn('Грешка при запазване на настройките на сървъра:', error);
                    });
                } catch (error) {
                    console.warn('Грешка при изпращане на настройките към сървъра:', error);
                }
            },

            /**
             * Toggle за показване/скриване на field selector
             */
            productExportToggleFieldSelector: function() {
                const container = document.getElementById('field-selector-container');
                const toggleText = document.getElementById('toggle-field-text');
                const toggleIcon = document.getElementById('toggle-field-icon');

                if (container && toggleText && toggleIcon) {
                    const isHidden = container.classList.contains('hidden');

                    if (isHidden) {
                        container.classList.remove('hidden');
                        toggleText.textContent = 'Скрий опции';
                        toggleIcon.className = 'ri-arrow-up-s-line ml-1';
                    } else {
                        container.classList.add('hidden');
                        toggleText.textContent = 'Покажи опции';
                        toggleIcon.className = 'ri-arrow-down-s-line ml-1';
                    }
                }
            },

            /**
             * Обновяване на информацията за формата
             */
            productExportUpdateFormatInfo: function() {
                const selectedFormat = document.querySelector('input[name="export_format"]:checked');
                const infoElement = document.getElementById('format-info');

                if (!selectedFormat || !infoElement) return;

                const format = selectedFormat.value;
                let info = '';

                switch (format) {
                    case 'csv':
                        info = 'CSV формат е подходящ за импорт в Excel и други табличи програми.';
                        break;
                    case 'xml':
                        info = 'XML формат запазва структурата на данните и е подходящ за системна интеграция.';
                        break;
                    case 'xlsx':
                        info = 'XLSX формат е родният формат на Microsoft Excel.';
                        break;
                }

                infoElement.textContent = info;
            },

            /**
             * Обработка на изпращането на формата за експорт
             */
            productExportHandleExportSubmit: function(e) {
                e.preventDefault();

                const form = document.getElementById('export-form');
                if (!form) {
                    this.productExportShowError('Формата за експорт не е намерена');
                    return;
                }

                const formData = new FormData(form);

                // Инициализиране на състоянието ако не съществува
                if (!this.productExportState) {
                    this.productExportState = {
                        activeRequests: new Set(),
                        requestQueue: [],
                        selectedCategories: new Map(),
                        selectedProducts: new Map()
                    };
                }

                // Добавяме избраните категории
                const categoryIds = Array.from(this.productExportState.selectedCategories.keys());
                categoryIds.forEach(id => {
                    formData.append('selected_categories[]', id);
                });

                // Добавяме избраните продукти
                const productIds = Array.from(this.productExportState.selectedProducts.keys());
                productIds.forEach(id => {
                    formData.append('selected_products[]', id);
                });

                // Валидация премахната - позволяваме експорт на всички продукти при липса на избор
                // Ако няма избрани категории или продукти, ще се експортират всички активни продукти

                this.productExportPerformExport(formData);
            },

            /**
             * Изпълнение на експорта
             */
            productExportPerformExport: function(formData) {
                const submitBtn = document.getElementById('export-submit-btn');
                const originalText = submitBtn ? submitBtn.textContent : '';

                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.textContent = 'Експортиране...';
                }

                const userToken = this.getUserToken();
                const timestamp = Date.now();
                const exportUrl = `index.php?route=catalog/product/export/process&user_token=${userToken}&_=${timestamp}`;

                fetch(exportUrl, {
                    method: 'POST',
                    body: formData,
                    cache: 'no-store',
                    headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                })
                .then(response => {
                    // Проверяваме дали response е JSON (грешка или large export) или binary файл (успех)
                    const contentType = response.headers.get('content-type');
                    // console.log('Export Debug: Response Content-Type:', contentType);
                    // console.log('Export Debug: Response status:', response.status);
                    // console.log('Export Debug: Response headers:', [...response.headers.entries()]);

                    if (contentType && contentType.includes('application/json')) {
                        // JSON response - може да е грешка или large export
                        return response.json().then(data => {
                            // console.log('Export Debug: Получен JSON response:', data);
                            // alert('DEBUG: Получен JSON response - large_export: ' + (data.large_export ? 'TRUE' : 'FALSE') + ', error: ' + (data.error || 'NONE'));

                            if (data.error) {
                                console.error('Export Debug: Грешка в response:', data.error);
                                // alert('DEBUG: Грешка в response: ' + data.error);
                                throw new Error(data.error);
                            }

                            // Проверяваме дали е large export
                            if (data.large_export) {
                                console.log('Export Debug: Задействане на large export режим:', data);
                                // alert('DEBUG: Задействане на large export режим!');
                                this.productExportHandleLargeExport(data);
                                return;
                            }

                            console.error('Export Debug: Неочакван JSON response:', data);
                            // alert('DEBUG: Неочакван JSON response: ' + JSON.stringify(data));
                            throw new Error('Неочакван JSON response');
                        });
                    } else {
                        // Binary response - файл за download
                        if (!response.ok) {
                            throw new Error('Грешка при генериране на файла');
                        }

                        // Получаваме името на файла от Content-Disposition хедъра
                        const contentDisposition = response.headers.get('content-disposition');
                        let fileName = 'export_products.csv'; // default

                        if (contentDisposition) {
                            const fileNameMatch = contentDisposition.match(/filename="([^"]+)"/);
                            if (fileNameMatch) {
                                fileName = fileNameMatch[1];
                            }
                        }

                        // Конвертираме response в blob и инициираме download
                        return response.blob().then(blob => {
                            this.productExportDownloadBlob(blob, fileName);

                            // Показваме успешно съобщение
                            this.productExportShowSuccess('Експортът завърши успешно');

                            // Показваме статистики
                            const selectedCategoriesCount = this.productExportState ? this.productExportState.selectedCategories.size : 0;
                            const selectedProductsCount = this.productExportState ? this.productExportState.selectedProducts.size : 0;
                            const totalSelected = selectedCategoriesCount + selectedProductsCount;

                            this.productExportShowStatistics({
                                total_exported: totalSelected > 0 ? `${totalSelected} избрани критерия` : 'Всички продукти',
                                format: fileName.split('.').pop().toUpperCase(),
                                file_name: fileName,
                                file_size: blob.size
                            });
                        });
                    }
                })
                .catch(error => {
                    this.productExportShowError(error.message || 'Възникна грешка при експорта');
                })
                .finally(() => {
                    if (submitBtn) {
                        submitBtn.disabled = false;
                        submitBtn.textContent = originalText;
                    }
                });
            },

            /**
             * Стартиране на сваляне на файла от URL
             */
            productExportInitiateDownload: function(url) {
                const link = document.createElement('a');
                link.href = url;
                link.download = '';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            },

            /**
             * Стартиране на сваляне на blob като файл
             */
            productExportDownloadBlob: function(blob, fileName) {
                try {
                    // Създаваме URL за blob-а
                    const url = window.URL.createObjectURL(blob);

                    // Създаваме временен link елемент
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = fileName;
                    link.style.display = 'none';

                    // Добавяме го към DOM, кликваме и го премахваме
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // Освобождаваме URL-а след кратко забавяне
                    setTimeout(() => {
                        window.URL.revokeObjectURL(url);
                    }, 100);

                } catch (error) {
                    this.productExportShowError('Грешка при сваляне на файла');
                }
            },

            /**
             * Показване на статистики
             */
            productExportShowStatistics: function(stats) {
                const statsContainer = document.getElementById('export-statistics');
                if (!statsContainer) return;

                statsContainer.innerHTML = `
                    <div class="alert alert-info">
                        <h5>Статистики за експорта:</h5>
                        <ul>
                            <li>Експортирани продукти: ${stats.total_exported}</li>
                            <li>Формат: ${stats.format}</li>
                            <li>Име на файл: ${stats.file_name}</li>
                            <li>Размер на файл: ${this.productExportFormatFileSize(stats.file_size)}</li>
                        </ul>
                    </div>
                `;
                statsContainer.style.display = 'block';
            },

            /**
             * Форматиране на размера на файла
             */
            productExportFormatFileSize: function(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            },

            /**
             * Обработка на опашката със заявки
             */
            productExportProcessQueue: function() {
                if (!this.productExportState) return;

                if (this.productExportState.requestQueue.length > 0 && this.productExportState.activeRequests.size < 10) { // maxConcurrentRequests
                    const nextRequest = this.productExportState.requestQueue.shift();
                    nextRequest();
                }
            },

            /**
             * Escape HTML за безопасност
             */
            productExportEscapeHtml: function(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            },

            /**
             * Показване на грешка
             */
            productExportShowError: function(message) {
                this.productExportShowAlert(message, 'error');
            },

            /**
             * Показване на успех
             */
            productExportShowSuccess: function(message) {
                this.productExportShowAlert(message, 'success');
            },

            /**
             * Показване на alert съобщение
             */
            productExportShowAlert: function(message, type = 'info') {
                // Проверяваме дали showAlert метода съществува и е функция
                if (this.showAlert && typeof this.showAlert === 'function') {
                    try {
                        this.showAlert(type, message);
                        return;
                    } catch (e) {
                        console.error('Error calling showAlert:', e);
                        // Продължаваме към fallback
                    }
                }

                // Fallback към стандартен alert
                const alertContainer = document.getElementById('alert-container') || document.body;

                const alert = document.createElement('div');
                alert.className = `alert alert-${type} alert-dismissible fade show`;
                alert.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                alertContainer.appendChild(alert);

                // Автоматично премахване след 5 секунди
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 5000);
            },

            /**
             * Обработва голям експорт с progress bar
             */
            productExportHandleLargeExport: function(exportData) {
                console.log('Large Export Debug: Започване на обработка на голям експорт', exportData);
                // alert('DEBUG: productExportHandleLargeExport() е извикана! exportData: ' + JSON.stringify(exportData));

                // Показваме progress bar интерфейса
                this.productExportShowProgressBar(exportData);

                // Започваме batch обработката
                this.productExportProcessBatches(exportData);
            },

            /**
             * Показва progress bar интерфейса
             */
            productExportShowProgressBar: function(exportData) {
                console.log('Progress Bar Debug: Показване на progress bar', exportData);
                // alert('DEBUG: productExportShowProgressBar() е извикана!');

                // Намираме export формата и нейния контейнер
                const exportForm = document.getElementById('export-form');
                let exportFormContainer = null;

                if (exportForm) {
                    console.log('Progress Bar Debug: Намерен export form');
                    // Намираме контейнера на формата (div с класове bg-white rounded-lg shadow-sm)
                    exportFormContainer = exportForm.closest('.bg-white.rounded-lg.shadow-sm');
                    if (!exportFormContainer) {
                        // Fallback - използваме родителския контейнер
                        exportFormContainer = exportForm.parentElement;
                    }
                } else {
                    console.error('Progress Bar Debug: export-form не е намерен');
                }

                // Проверяваме дали вече има progress bar
                const existingProgress = document.getElementById('large-export-progress');
                if (existingProgress) {
                    console.log('Progress Bar Debug: Премахване на съществуващ progress bar');
                    existingProgress.remove();
                }

                // Създаваме progress bar контейнера
                console.log('Progress Bar Debug: Създаване на progress container');
                const progressContainer = document.createElement('div');
                progressContainer.id = 'large-export-progress';
                progressContainer.className = 'bg-white rounded-lg shadow-sm p-6 mb-6';

                // КРИТИЧНО: Принудително задаваме CSS стилове за видимост
                progressContainer.style.cssText = `
                    display: block !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                    position: relative !important;
                    z-index: 1000 !important;
                    background-color: white !important;
                    border: 1px solid #e5e7eb !important;
                    border-radius: 8px !important;
                    padding: 24px !important;
                    margin-bottom: 24px !important;
                    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
                    width: 100% !important;
                    max-width: none !important;
                `;
                progressContainer.innerHTML = `
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-800">Експортиране на продукти</h3>
                        <button type="button" id="cancel-export-btn" class="text-red-600 hover:text-red-800 text-sm">
                            <i class="ri-close-line mr-1"></i>Отказ
                        </button>
                    </div>

                    <div class="space-y-4">
                        <div class="flex justify-between text-sm text-gray-600">
                            <span>Прогрес:</span>
                            <span id="progress-text">0 от ${exportData.total_products} продукта</span>
                        </div>

                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div id="progress-bar" class="bg-blue-600 h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>

                        <div class="flex justify-between text-xs text-gray-500">
                            <span id="batch-info">Batch 0 от ${exportData.total_batches}</span>
                            <span id="progress-percentage">0%</span>
                        </div>

                        <div class="text-xs text-gray-400" id="memory-info"></div>
                    </div>
                `;

                // Вмъкваме progress bar-а СЛЕД export формата
                console.log('Progress Bar Debug: Търсене на място за вмъкване след export form');

                if (exportFormContainer) {
                    // Намираме родителския контейнер на export формата
                    const parentContainer = exportFormContainer.parentElement;
                    console.log('Progress Bar Debug: Вмъкване на progress bar след export form container');

                    // Вмъкваме progress bar след контейнера на формата
                    if (exportFormContainer.nextSibling) {
                        parentContainer.insertBefore(progressContainer, exportFormContainer.nextSibling);
                    } else {
                        parentContainer.appendChild(progressContainer);
                    }
                } else {
                    // Fallback - търсим основния контейнер на страницата
                    console.log('Progress Bar Debug: Fallback - търсене на основен контейнер');
                    const mainContainer = document.querySelector('.p-6') || // Main content div
                                        document.querySelector('main') ||
                                        document.querySelector('.container-fluid') ||
                                        document.body;
                    console.log('Progress Bar Debug: Добавяне към основен контейнер');
                    mainContainer.appendChild(progressContainer);
                }

                console.log('Progress Bar Debug: Progress bar е добавен към DOM');

                // КРИТИЧНО: Добавяме подробна диагностика на DOM елемента
                setTimeout(() => {
                    const addedElement = document.getElementById('large-export-progress');
                    if (addedElement) {
                        console.log('Progress Bar Debug: ✅ DOM елементът е създаден успешно');
                        console.log('Progress Bar Debug: Element position:', addedElement.getBoundingClientRect());
                        console.log('Progress Bar Debug: Element styles:', window.getComputedStyle(addedElement));
                        console.log('Progress Bar Debug: Element parent:', addedElement.parentElement);
                        console.log('Progress Bar Debug: Element visibility:', addedElement.style.display, addedElement.style.visibility);

                        // Принудително правим елемента видим
                        addedElement.style.display = 'block';
                        addedElement.style.visibility = 'visible';
                        addedElement.style.opacity = '1';
                        addedElement.style.zIndex = '1000';
                        // addedElement.style.backgroundColor = 'red'; // Временно за видимост - ПРЕМАХНАТО
                        // addedElement.style.border = '3px solid blue'; // Временно за видимост - ПРЕМАХНАТО

                        console.log('Progress Bar Debug: ✅ Принудително направен видим');
                    } else {
                        console.error('Progress Bar Debug: ❌ DOM елементът НЕ е намерен след добавяне!');
                    }
                }, 100);

                // Добавяме event listener за отказ
                const cancelBtn = document.getElementById('cancel-export-btn');
                if (cancelBtn) {
                    console.log('Progress Bar Debug: Добавяне на cancel event listener');
                    cancelBtn.addEventListener('click', () => {
                        this.productExportCancelLargeExport();
                    });
                } else {
                    console.error('Progress Bar Debug: Cancel button не е намерен');
                }
            },

            /**
             * Обработва batch-овете последователно
             */
            productExportProcessBatches: function(exportData) {
                this.largeExportData = exportData;
                this.largeExportData.cancelled = false;
                this.largeExportData.currentBatch = 0;

                // Започваме обработката на първия batch
                this.productExportProcessNextBatch();
            },

            /**
             * Опитва се да обнови user token-а
             */
            productExportRefreshUserToken: function() {
                console.log('Token Debug: Опит за обновяване на user token');

                // Първо опитваме стандартния refresh endpoint
                return fetch('index.php?route=common/login/refresh', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Cache-Control': 'no-cache'
                    },
                    cache: 'no-store'
                })
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    } else {
                        throw new Error('Refresh endpoint не е наличен');
                    }
                })
                .then(data => {
                    if (data.user_token) {
                        console.log('Token Debug: ✅ Успешно обновен user token чрез refresh');
                        // Обновяваме token-а в hidden полето
                        const tokenInput = document.querySelector('input[name="user_token"]');
                        if (tokenInput) {
                            tokenInput.value = data.user_token;
                        }
                        return data.user_token;
                    } else {
                        throw new Error('Неуспешно обновяване на token');
                    }
                })
                .catch(error => {
                    console.log('Token Debug: Refresh endpoint неуспешен, опитваме алтернативен подход');

                    // Алтернативен подход - опитваме се да получим нов token чрез dashboard заявка
                    return fetch('index.php?route=common/dashboard', {
                        method: 'GET',
                        cache: 'no-store',
                        headers: { 'Cache-Control': 'no-cache' }
                    })
                    .then(response => response.text())
                    .then(html => {
                        // Търсим user_token в HTML-а
                        const tokenMatch = html.match(/user_token['"]\s*:\s*['"]([^'"]+)['"]/);
                        if (tokenMatch && tokenMatch[1]) {
                            const newToken = tokenMatch[1];
                            console.log('Token Debug: ✅ Успешно извлечен token от dashboard');

                            // Обновяваме token-а в hidden полето
                            const tokenInput = document.querySelector('input[name="user_token"]');
                            if (tokenInput) {
                                tokenInput.value = newToken;
                            }
                            return newToken;
                        } else {
                            throw new Error('Не може да се извлече нов token');
                        }
                    })
                    .catch(altError => {
                        console.error('Token Debug: ❌ И алтернативният подход неуспешен:', altError);
                        throw new Error('Неуспешно обновяване на token с всички методи');
                    });
                });
            },

            /**
             * Обработва следващия batch
             */
            productExportProcessNextBatch: function() {
                if (!this.largeExportData || this.largeExportData.cancelled) {
                    return;
                }

                const currentBatch = this.largeExportData.currentBatch;
                const totalBatches = this.largeExportData.total_batches;

                if (currentBatch >= totalBatches) {
                    // Всички batch-ове са обработени, финализираме експорта
                    this.productExportFinalizeLargeExport();
                    return;
                }

                // Обновяваме progress bar
                // this.productExportUpdateProgress(currentBatch, 0, 'Обработва се batch ' + (currentBatch + 1));

                // Изпращаме заявка за обработка на текущия batch
                const formData = new FormData();
                formData.append('export_id', this.largeExportData.export_id);
                formData.append('batch_index', currentBatch);
                // formData.append('user_token', this.getUserToken());

                const userToken = this.getUserToken();
                const timestamp = Date.now();
                const batchUrl = `index.php?route=catalog/product/export/processBatch&user_token=${userToken}&_=${timestamp}`;

                fetch(batchUrl, {
                    method: 'POST',
                    body: formData,
                    cache: 'no-store',
                    headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                })
                .then(response => {
                    // console.log('Batch Debug: Response URL:', response.url);
                    // console.log('Batch Debug: Response status:', response.status);
                    // console.log('Batch Debug: Response headers:', response.headers);

                    // Проверяваме дали response е redirect към login
                    if (response.url && response.url.includes('login')) {
                        console.error('Batch Debug: ❌ Redirect към login страница!');
                        throw new Error('Сесията е изтекла. Моля влезте отново в системата.');
                    }

                    // Проверяваме статус кода
                    if (response.status === 302 || response.status === 401) {
                        console.error('Batch Debug: ❌ Unauthorized или redirect status:', response.status);
                        throw new Error('Сесията е изтекла. Моля влезте отново в системата.');
                    }

                    // Проверяваме дали response е JSON
                    const contentType = response.headers.get('content-type');
                    // console.log('Batch Debug: Content-Type:', contentType);

                    if (!contentType || !contentType.includes('application/json')) {
                        console.error('Batch Debug: ❌ Неочакван Content-Type:', contentType);

                        // Опитваме се да прочетем response като текст за диагностика
                        return response.text().then(text => {
                            console.error('Batch Debug: Response text:', text.substring(0, 500));
                            if (text.includes('login') || text.includes('Потребителско име')) {
                                throw new Error('Сесията е изтекла. Моля влезте отново в системата.');
                            }
                            throw new Error('Неочакван response тип. Възможно е сесията да е изтекла.');
                        });
                    }

                    return response.json();
                })
                .then(data => {
                    if (data.error) {
                        throw new Error(data.error);
                    }

                    // Обновяваме прогреса
                    this.productExportUpdateProgress(
                        data.processed_batches,
                        data.processed_products,
                        `Обработени ${data.processed_products} от ${data.total_products} продукта`
                    );

                    // Показваме memory информация ако е налична
                    if (data.memory_usage) {
                        this.productExportUpdateMemoryInfo(data.memory_usage, data.memory_peak);
                    }

                    // Преминаваме към следващия batch
                    this.largeExportData.currentBatch++;

                    // Малко забавяне преди следващия batch за да не претоварим сървъра
                    setTimeout(() => {
                        this.productExportProcessNextBatch();
                    }, 500);
                })
                .catch(error => {
                    console.error('Batch Debug: Грешка при обработка на batch:', error);

                    // Ако грешката е свързана със сесия, опитваме token refresh
                    if (error.message.includes('Сесията е изтекла') || error.message.includes('Неочакван response тип')) {
                        console.log('Batch Debug: Опит за token refresh и retry на batch');

                        this.productExportRefreshUserToken()
                            .then(() => {
                                console.log('Batch Debug: ✅ Token обновен, retry на batch');
                                // Retry същия batch с новия token
                                setTimeout(() => {
                                    this.productExportProcessNextBatch();
                                }, 1000);
                            })
                            .catch(tokenError => {
                                console.error('Batch Debug: ❌ Неуспешен token refresh:', tokenError);
                                this.productExportShowError(`Сесията е изтекла и не може да бъде обновена. Моля влезте отново в системата.`);
                                this.productExportCancelLargeExport();
                            });
                    } else {
                        // Други грешки - спираме експорта
                        this.productExportShowError('Грешка при обработка на batch: ' + error.message);
                        this.productExportCancelLargeExport();
                    }
                });
            },

            /**
             * Финализира големия експорт
             */
            productExportFinalizeLargeExport: function() {
                if (!this.largeExportData || this.largeExportData.cancelled) {
                    return;
                }

                // Обновяваме progress bar
                this.productExportUpdateProgress(
                    this.largeExportData.total_batches,
                    this.largeExportData.total_products,
                    'Финализиране на експорта...'
                );

                // Изпращаме заявка за финализиране
                const formData = new FormData();
                formData.append('export_id', this.largeExportData.export_id);
                // formData.append('user_token', this.getUserToken());

                const userToken = this.getUserToken();
                const timestamp = Date.now();
                const finalizeUrl = `index.php?route=catalog/product/export/finalizeLargeExport&user_token=${userToken}&_=${timestamp}`;

                fetch(finalizeUrl, {
                    method: 'POST',
                    body: formData,
                    cache: 'no-store',
                    headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                })
                .then(response => {
                    // Проверяваме дали response е JSON (грешка) или binary файл (успех)
                    const contentType = response.headers.get('content-type');

                    if (contentType && contentType.includes('application/json')) {
                        return response.json().then(data => {
                            if (data.error) {
                                throw new Error(data.error);
                            }
                            throw new Error('Неочакван JSON response при финализиране');
                        });
                    } else {
                        // Binary response - файл за download
                        if (!response.ok) {
                            throw new Error('Грешка при генериране на финалния файл');
                        }

                        // Получаваме името на файла от Content-Disposition хедъра
                        const contentDisposition = response.headers.get('content-disposition');
                        let fileName = 'export_products.csv'; // default

                        if (contentDisposition) {
                            const fileNameMatch = contentDisposition.match(/filename="([^"]+)"/);
                            if (fileNameMatch) {
                                fileName = fileNameMatch[1];
                            }
                        }

                        // Конвертираме response в blob и инициираме download
                        return response.blob().then(blob => {
                            this.productExportDownloadBlob(blob, fileName);

                            // Показваме успешно съобщение
                            this.productExportShowSuccess('Големият експорт завърши успешно');

                            // Показваме статистики
                            this.productExportShowStatistics({
                                total_exported: `${this.largeExportData.total_products} продукта`,
                                format: fileName.split('.').pop().toUpperCase(),
                                file_name: fileName,
                                file_size: blob.size
                            });

                            // Скриваме progress bar и показваме формата отново
                            this.productExportHideProgressBar();
                        });
                    }
                })
                .catch(error => {
                    this.productExportShowError('Грешка при финализиране на експорта: ' + error.message);
                    this.productExportCancelLargeExport();
                });
            },

            /**
             * Обновява progress bar-а
             */
            productExportUpdateProgress: function(processedBatches, processedProducts, statusText) {
                if (!this.largeExportData) return;

                const progressPercentage = Math.round((processedProducts / this.largeExportData.total_products) * 100);

                // Обновяваме progress bar
                const progressBar = document.getElementById('progress-bar');
                if (progressBar && progressPercentage > 0) {
                    progressBar.style.width = progressPercentage + '%';
                }

                // Обновяваме текста за прогрес
                const progressText = document.getElementById('progress-text');
                if (progressText) {
                    progressText.textContent = statusText || `${processedProducts} от ${this.largeExportData.total_products} продукта`;
                }

                // Обновяваме batch информацията
                const batchInfo = document.getElementById('batch-info');
                if (batchInfo) {
                    batchInfo.textContent = `Batch ${processedBatches} от ${this.largeExportData.total_batches}`;
                }

                // Обновяваме процентите
                const progressPercentageElement = document.getElementById('progress-percentage');
                if (progressPercentageElement && progressPercentage > 0) {
                    progressPercentageElement.textContent = progressPercentage + '%';
                }
            },

            /**
             * Обновява информацията за паметта
             */
            productExportUpdateMemoryInfo: function(memoryUsage, memoryPeak) {
                const memoryInfo = document.getElementById('memory-info');
                if (memoryInfo) {
                    const usageMB = Math.round(memoryUsage / 1024 / 1024);
                    const peakMB = Math.round(memoryPeak / 1024 / 1024);
                    memoryInfo.textContent = `Памет: ${usageMB}MB използвана, ${peakMB}MB пик`;
                }
            },

            /**
             * Отказва големия експорт
             */
            productExportCancelLargeExport: function() {
                if (this.largeExportData) {
                    this.largeExportData.cancelled = true;
                }

                this.productExportHideProgressBar();
                this.productExportShowError('Експортът беше отказан');
            },

            /**
             * Скрива progress bar и показва формата отново
             */
            productExportHideProgressBar: function() {
                const progressContainer = document.getElementById('large-export-progress');
                if (progressContainer) {
                    progressContainer.remove();
                }

                const exportForm = document.getElementById('export-form');
                if (exportForm) {
                    exportForm.style.display = 'block';
                }

                // Възстановяваме бутона за експорт
                const submitBtn = document.getElementById('export-submit-btn');
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.textContent = 'Започни експорт';
                }

                // Изчистваме данните за големия експорт
                this.largeExportData = null;
            }
        });
    }

})();

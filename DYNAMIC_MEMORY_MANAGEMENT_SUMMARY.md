# Dynamic Memory Management - Временно решение за Memory Exhaustion

## Проблем

Въпреки имплементирането на streaming merge архитектурата, все още получаваме PHP Fatal error за изчерпана памет по време на merge процеса. Като **временно решение**, имплементирах **динамично увеличаване на memory_limit** само за merge/finalization процеса.

### 🚨 **Persistent Memory Exhaustion:**
```
[16-Jul-2025 00:21:51 Europe/Sofia] PHP Fatal error: 
Allowed memory size of 134217728 bytes exhausted (tried to allocate 20480 bytes) 
in /home/<USER>/theme25/system/storage/vendor/phpoffice/PhpSpreadsheet/Collection/Cells.php on line 157
```

### 📊 **Анализ на проблема:**
- **Streaming merge архитектурата** намали memory usage, но не достатъчно
- **PhpSpreadsheet библиотеката** все още изисква значителна памет за XLSX операции
- **128MB memory limit** е недостатъчен за обработка на 9777 продукта
- **Merge процесът** е критичната точка където се изчерпва паметта

## Решение

### ✅ **Dynamic Memory Limit Management**

Имплементирах **интелигентно динамично управление на memory_limit** с **comprehensive fallback стратегия**:

#### **🎯 Основна концепция:**

```
1. Pre-Merge Phase:
   ├── Запазване на оригиналния memory_limit (128MB)
   ├── Динамично увеличаване на memory_limit (512MB/1024MB/2048MB)
   └── Validation на успешното увеличение

2. Merge Phase:
   ├── Streaming merge с увеличен memory_limit
   ├── Enhanced memory monitoring
   └── Comprehensive error handling

3. Post-Merge Phase:
   ├── Възстановяване на оригиналния memory_limit
   ├── Memory cleanup и garbage collection
   └── Detailed memory statistics

4. Fallback Strategy (при неуспех):
   ├── Extreme memory increase (2048MB)
   ├── CSV export като алтернатива
   └── ZIP archive с batch файлове
```

#### **🔧 Ключови компоненти:**

#### **1. 📝 Enhanced mergeBatchXlsxFiles():**

**ПРЕДИ:**
```php
// ❌ Статичен memory limit
private function mergeBatchXlsxFiles($exportInfo) {
    return $this->streamingMergeViaCsv($exportInfo, $tempDir, $totalBatches);
}
```

**СЛЕД:**
```php
// ✅ Динамично memory управление
private function mergeBatchXlsxFiles($exportInfo) {
    // Запазваме оригиналния memory limit
    $originalMemoryLimit = ini_get('memory_limit');
    
    // Динамично увеличаване на memory_limit за merge процеса
    $newMemoryLimit = $this->increaseMemoryLimitForMerge($originalMemoryLimit);
    
    try {
        // Streaming merge с увеличен memory_limit
        $result = $this->streamingMergeViaCsv($exportInfo, $tempDir, $totalBatches);
        return $result;
        
    } catch (\Exception $e) {
        F()->log->developer("XLSX Dynamic Merge: Грешка по време на merge: " . $e->getMessage(), __FILE__, __LINE__);
        throw $e;
        
    } finally {
        // Винаги възстановяваме оригиналния memory limit
        $this->restoreOriginalMemoryLimit($originalMemoryLimit);
    }
}
```

#### **2. 🏗️ Smart Memory Limit Calculation:**

```php
/**
 * Увеличава memory_limit динамично за merge процеса
 */
private function increaseMemoryLimitForMerge($originalMemoryLimit) {
    $originalBytes = $this->parseMemoryLimit($originalMemoryLimit);
    $originalMB = round($originalBytes / 1024 / 1024, 0);
    
    // Определяме новия memory limit базиран на текущия
    $newMemoryLimitMB = $this->calculateOptimalMemoryLimit($originalMB);
    $newMemoryLimit = $newMemoryLimitMB . 'M';
    
    F()->log->developer("Memory Management: Опит за увеличаване от {$originalMemoryLimit} ({$originalMB}MB) на {$newMemoryLimit}", __FILE__, __LINE__);
    
    // Опитваме се да увеличим memory_limit
    $success = ini_set('memory_limit', $newMemoryLimit);
    
    if ($success !== false) {
        $actualNewLimit = ini_get('memory_limit');
        $actualNewBytes = $this->parseMemoryLimit($actualNewLimit);
        $actualNewMB = round($actualNewBytes / 1024 / 1024, 0);
        
        if ($actualNewBytes > $originalBytes) {
            F()->log->developer("Memory Management: ✅ Memory limit увеличен успешно от {$originalMB}MB на {$actualNewMB}MB", __FILE__, __LINE__);
            F()->log->developer("Memory Management: Налична допълнителна памет: " . ($actualNewMB - $originalMB) . "MB", __FILE__, __LINE__);
            return $actualNewLimit;
        } else {
            F()->log->developer("Memory Management: ⚠️ Memory limit не се промени - остава {$actualNewMB}MB", __FILE__, __LINE__);
            return $originalMemoryLimit;
        }
    } else {
        F()->log->developer("Memory Management: ❌ Неуспешно увеличаване на memory_limit - ini_set() върна false", __FILE__, __LINE__);
        return $originalMemoryLimit;
    }
}

/**
 * Изчислява оптималния memory limit за merge процеса
 */
private function calculateOptimalMemoryLimit($currentMB) {
    if ($currentMB <= 128) {
        return 512;      // 128MB → 512MB (4x увеличение)
    } elseif ($currentMB <= 256) {
        return 1024;     // 256MB → 1024MB (4x увеличение)
    } elseif ($currentMB <= 512) {
        return 1536;     // 512MB → 1536MB (3x увеличение)
    } else {
        return $currentMB * 2;  // За по-големи стойности удвояваме
    }
}
```

#### **3. 🔄 Automatic Memory Restoration:**

```php
/**
 * Възстановява оригиналния memory_limit
 */
private function restoreOriginalMemoryLimit($originalMemoryLimit) {
    F()->log->developer("Memory Management: Възстановяване на оригиналния memory_limit: {$originalMemoryLimit}", __FILE__, __LINE__);
    
    $currentLimit = ini_get('memory_limit');
    
    if ($currentLimit !== $originalMemoryLimit) {
        $success = ini_set('memory_limit', $originalMemoryLimit);
        
        if ($success !== false) {
            $restoredLimit = ini_get('memory_limit');
            if ($restoredLimit === $originalMemoryLimit) {
                F()->log->developer("Memory Management: ✅ Memory limit възстановен успешно на {$originalMemoryLimit}", __FILE__, __LINE__);
            } else {
                F()->log->developer("Memory Management: ⚠️ Memory limit частично възстановен - очакван: {$originalMemoryLimit}, действителен: {$restoredLimit}", __FILE__, __LINE__);
            }
        } else {
            F()->log->developer("Memory Management: ❌ Неуспешно възстановяване на memory_limit", __FILE__, __LINE__);
        }
    }
    
    // Финален memory monitoring
    $finalMemory = memory_get_usage(true);
    $finalPeak = memory_get_peak_usage(true);
    F()->log->developer("Memory Management: Финална memory статистика - текуща: " . round($finalMemory / 1024 / 1024, 2) . "MB, peak: " . round($finalPeak / 1024 / 1024, 2) . "MB", __FILE__, __LINE__);
}
```

#### **4. 🛡️ Comprehensive Fallback Strategy:**

```php
/**
 * Обработва memory exhaustion грешки с fallback опции
 */
private function handleMemoryExhaustionFallback($exportInfo, $originalException) {
    // Опция 1: Extreme memory increase (2048MB)
    if ($currentMemoryLimitMB < 2048) {
        $extremeMemoryLimit = '2048M';
        $success = ini_set('memory_limit', $extremeMemoryLimit);
        
        if ($success !== false) {
            try {
                gc_collect_cycles();
                $mergedRows = $this->streamingMergeViaCsv($exportInfo, $tempDir, $totalBatches);
                F()->log->developer("Memory Fallback: ✅ Merge успешен с увеличен memory limit", __FILE__, __LINE__);
                return; // Успешно завършване
            } catch (\Exception $e) {
                F()->log->developer("Memory Fallback: ❌ Merge неуспешен дори с увеличен memory limit", __FILE__, __LINE__);
            }
        }
    }
    
    // Опция 2: CSV експорт като алтернатива
    try {
        $csvFileName = str_replace('.xlsx', '.csv', $exportInfo['final_file_name']);
        $csvFilePath = $tempDir . $csvFileName;
        
        $totalMergedRows = $this->extractAllBatchesToCsv($tempDir, $totalBatches, $csvFilePath);
        
        // Обновяваме export info за CSV формат
        $exportInfo['format'] = 'csv';
        $exportInfo['final_file_name'] = $csvFileName;
        $exportInfo['fallback_reason'] = 'memory_exhaustion_xlsx_merge';
        
        F()->log->developer("Memory Fallback: ✅ Fallback към CSV формат завършен успешно", __FILE__, __LINE__);
        return; // Успешно завършване с CSV
        
    } catch (\Exception $csvException) {
        F()->log->developer("Memory Fallback: ❌ CSV fallback също неуспешен", __FILE__, __LINE__);
    }
    
    // Опция 3: ZIP архив с batch файлове
    try {
        $zipFileName = str_replace('.xlsx', '_batches.zip', $exportInfo['final_file_name']);
        $zipFilePath = $tempDir . $zipFileName;
        
        $this->createBatchFilesZip($tempDir, $totalBatches, $zipFilePath);
        
        // Обновяваме export info за ZIP формат
        $exportInfo['format'] = 'zip';
        $exportInfo['final_file_name'] = $zipFileName;
        $exportInfo['fallback_reason'] = 'memory_exhaustion_all_formats';
        
        F()->log->developer("Memory Fallback: ✅ ZIP архив създаден успешно", __FILE__, __LINE__);
        return; // Успешно завършване с ZIP
        
    } catch (\Exception $zipException) {
        F()->log->developer("Memory Fallback: ❌ ZIP fallback също неуспешен", __FILE__, __LINE__);
    }
    
    // Ако всички fallback опции се провалят
    throw new \Exception("Memory exhaustion при XLSX merge. Всички fallback опции също се провалиха.");
}
```

#### **5. 📊 Enhanced Memory Monitoring:**

```php
// Pre-merge monitoring
"XLSX Dynamic Merge: Memory преди merge - XMB от 128M (Y%)"
"Memory Management: Опит за увеличаване от 128M (128MB) на 512M"
"Memory Management: ✅ Memory limit увеличен успешно от 128MB на 512MB"

// During merge monitoring
"CSV Streaming Merge: Започване с memory limit: 512M, използвани: XMB (Y%)"
"CSV Streaming Merge: Memory след CSV extraction - XMB (използвани YMB за extraction)"
"CSV Streaming Merge: Memory след XLSX conversion - XMB (използвани YMB за conversion)"

// Post-merge monitoring
"Memory Management: Възстановяване на оригиналния memory_limit: 128M"
"Memory Management: ✅ Memory limit възстановен успешно на 128M"
"Memory Management: Финална memory статистика - текуща: XMB, peak: YMB"
```

### ✅ **Ключови предимства**

#### **🎯 Intelligent Memory Scaling:**
- ✅ **Automatic detection** - определя оптималния memory limit базиран на текущия
- ✅ **Progressive scaling** - 128MB→512MB→1024MB→2048MB според нуждите
- ✅ **Validation checks** - проверява дали увеличението е успешно
- ✅ **Graceful degradation** - работи дори ако увеличението не е възможно

#### **🛡️ Comprehensive Safety:**
- ✅ **Try-catch-finally** - гарантира възстановяване на memory_limit
- ✅ **Original limit restoration** - винаги възстановява оригиналната стойност
- ✅ **Error isolation** - грешки в merge не влияят на memory restoration
- ✅ **Detailed logging** - comprehensive tracking на всички промени

#### **🔄 Multi-Level Fallback:**
- ✅ **Level 1:** Standard memory increase (512MB)
- ✅ **Level 2:** Extreme memory increase (2048MB)
- ✅ **Level 3:** CSV export fallback
- ✅ **Level 4:** ZIP archive fallback

#### **📊 Enhanced Monitoring:**
- ✅ **Real-time tracking** - memory usage преди/по време/след merge
- ✅ **Peak memory detection** - memory_get_peak_usage() monitoring
- ✅ **Detailed statistics** - comprehensive memory reporting
- ✅ **Fallback reasoning** - ясно обяснение защо е използван fallback

## Тестов план

### Стъпка 1: Dynamic Memory Increase Validation
1. **Започнете XLSX експорт** на всички 9777 продукта
2. **Изчакайте създаването на всички batch файлове** (98 файла с batch size 100)
3. **Наблюдавайте merge процеса:**
   - "Memory Management: Опит за увеличаване от 128M (128MB) на 512M"
   - "Memory Management: ✅ Memory limit увеличен успешно от 128MB на 512MB"
   - "CSV Streaming Merge: Започване с memory limit: 512M"

### Стъпка 2: Memory Usage Monitoring
1. **Проследете memory usage** по време на merge:
   - Memory трябва да остане под новия лимит (512MB)
   - "CSV Streaming Merge: Memory след CSV extraction - XMB"
   - "CSV Streaming Merge: Memory след XLSX conversion - XMB"
2. **Валидирайте memory restoration** - трябва да се възстанови на 128M

### Стъпка 3: Fallback Testing (ако е нужно)
1. **Ако merge се провали** с 512MB, наблюдавайте fallback:
   - "Memory Fallback: Опит за увеличаване на memory limit до 2048MB"
   - "Memory Fallback: ✅ Merge успешен с увеличен memory limit"
2. **Ако всички memory увеличения се провалят:**
   - "Memory Fallback: Предлагане на CSV експорт като алтернатива"
   - "Memory Fallback: ✅ Fallback към CSV формат завършен успешно"

### Стъпка 4: Final Validation
1. **Проверете финалния файл** - трябва да съдържа всички 9777 продукта
2. **Валидирайте memory restoration** - memory_limit трябва да е 128M
3. **Потвърдете cleanup** - временните файлове трябва да са изтрити

## Очаквани резултати

### ✅ **При успешно тестване:**

#### **Memory management:**
```
✅ Memory limit се увеличава успешно от 128MB на 512MB
✅ Merge процесът завършва без memory exhaustion
✅ Memory limit се възстановява правилно на 128MB
✅ Detailed memory statistics се логват правилно
```

#### **Process flow:**
```
✅ Всички 98 batch файла се обединяват успешно
✅ Streaming merge работи с увеличения memory limit
✅ Финалният XLSX файл се създава правилно
✅ Cleanup процесът завършва успешно
```

#### **Fallback readiness:**
```
✅ Fallback логиката е готова ако memory увеличението не стигне
✅ CSV export опцията е налична като алтернатива
✅ ZIP archive опцията е налична като последна възможност
✅ Comprehensive error handling покрива всички сценарии
```

### 🚨 **Ако има проблеми:**

#### **Memory увеличението не работи:**
```
❌ Проверете server ограниченията за memory_limit
❌ Проверете дали има suhosin extension ограничения
❌ Проверете логовете за ini_set() грешки
```

#### **Merge все още се провалява:**
```
❌ Проверете дали fallback логиката се активира
❌ Проверете дали CSV export работи като алтернатива
❌ Проверете дали ZIP archive се създава правилно
```

## Заключение

Това **динамично memory management решение** осигурява:

1. ✅ **Intelligent memory scaling** - автоматично увеличаване според нуждите
2. ✅ **Safe memory restoration** - винаги възстановява оригиналния лимит
3. ✅ **Comprehensive fallback** - multiple опции при неуспех
4. ✅ **Enhanced monitoring** - detailed memory tracking

Това **временно решение** трябва да позволи **успешен експорт на всички 9777 продукта** докато не намерим по-ефективен подход за merge процеса! 🎯

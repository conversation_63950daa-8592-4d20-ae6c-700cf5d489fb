<?php

namespace Theme25\Backend\Model\Catalog;

class Productexportxml extends \Model {

    private $languages = [];
    private $languageCount = 0;
    private $currentLanguageId = 1;
    private $dataModel;
    private $fieldHelper;

    public function __construct($registry) {
        parent::__construct($registry);

        // Агресивно изчистване на opcache за този файл
        if (function_exists('opcache_invalidate')) {
            opcache_invalidate(__FILE__, true);
        }
        if (function_exists('opcache_reset')) {
            opcache_reset();
        }
        $this->dataModel = $this->registry->get('exportDataModel');
        $this->fieldHelper = $this->registry->get('exportFieldHelper');
    }

    /**
     * Задава централизираната fieldHelper инстанция
     */
    public function setFieldHelper($fieldHelper) {
        if($this->fieldHelper) return;
        $this->fieldHelper = $fieldHelper;
    }

    /**
     * Задава централизираната dataModel инстанция
     */
    public function setDataModel($dataModel) {
        if(!$this->dataModel) {
            $this->dataModel = $dataModel;
        }

        // Синхронизираме language_id ако е зададен
        if (is_callable([$this, 'getLanguageId']) && is_callable([$dataModel, 'setLanguageId'])) {
            $dataModel->setLanguageId($this->getLanguageId());
        }
    }

    /**
     * Генерира XML файл с продукти
     */
    public function generateFile($products, $filePath, $languageId = null, $exportData = []) {

        F()->log->developer("XML Export: generateFile() започва - файлова версия " . date('Y-m-d H:i:s', filemtime(__FILE__)), __FILE__, __LINE__);


        // Изчистваме opcache за този файл ако е възможно
        if (function_exists('opcache_invalidate')) {
            opcache_invalidate(__FILE__, true);
        }

        F()->log->developer('>>>> 1', __FILE__, __LINE__);

        // Проверяваме дали dataModel е зададен
        if (!$this->dataModel) {
            throw new \Exception('DataModel не е инициализиран в Productexportxml');
        }

        F()->log->developer('>>>> 2', __FILE__, __LINE__);

        // Инициализираме езиците и текущия език
        // $this->initializeLanguages($languageId);

        F()->log->developer('>>>> 3', __FILE__, __LINE__);


        // Диагностика на експорта
        $diagnosis = $this->fieldHelper->diagnoseExport($exportData, 'xml');

        F()->log->developer('>>>> 5', __FILE__, __LINE__);

        // Обработваме данните за експорт и определяме избраните полета
        $selectedFields = $this->processExportData($exportData);

        F()->log->developer($selectedFields, __FILE__, __LINE__);

        F()->log->developer('>>>> 6', __FILE__, __LINE__);

        // Създаваме XML документ
        $xml = new \DOMDocument('1.0', 'UTF-8');
        $xml->formatOutput = true;

        // Създаваме root елемент
        $root = $xml->createElement('products');
        $xml->appendChild($root);

        // Извличаме ID-тата на продуктите
        $productIds = array_column($products, 'product_id');

        F()->log->developer( $productIds, __FILE__, __LINE__);

        F()->log->developer('>>>> 7', __FILE__, __LINE__);

        // Подготвяме опциите за централизираното извличане на данни
        $options = $this->prepareDataOptions($exportData);

        F()->log->developer('>>>> 8', __FILE__, __LINE__);

        // Извличаме всички данни централизирано с batch обработка
        $enrichedProducts = $this->dataModel->getProductsForExport($productIds, $options);

        F()->log->developer('>>>> 9', __FILE__, __LINE__);

        // Добавяме продуктите
        foreach ($enrichedProducts as $product) {
            $productElement = $this->createProductElement($xml, $product, $selectedFields);
            $root->appendChild($productElement);
        }

        F()->log->developer('>>>> 10', __FILE__, __LINE__);

        F()->log->developer($filePath, __FILE__, __LINE__);

        // Записваме файла
        $xml->save($filePath);

        F()->log->developer('>>>> 11', __FILE__, __LINE__);
    }

    /**
     * Създава празен XML файл с правилна структура за batch експорт
     */
    public function initializeBatchXmlFile($filePath, $exportData = []) {
        F()->log->developer("XML Batch: Създаване на празен XML файл: " . $filePath, __FILE__, __LINE__);

        // Обработваме данните за експорт и определяме избраните полета
        $selectedFields = $this->processExportData($exportData);

        // Създаваме XML документ
        $xml = new \DOMDocument('1.0', 'UTF-8');
        $xml->formatOutput = true;

        // Създаваме root елемент
        $root = $xml->createElement('products');
        $xml->appendChild($root);

        // Добавяме коментар с header информация за debug
        $headerComment = $xml->createComment('Selected Fields: ' . implode(', ', $selectedFields));
        $root->appendChild($headerComment);

        // Записваме файла
        $xml->save($filePath);

        F()->log->developer("XML Batch: Празен XML файл създаден с " . count($selectedFields) . " полета", __FILE__, __LINE__);
        return true;
    }

    /**
     * Добавя batch продукти към съществуващ XML файл
     */
    public function appendBatchToXmlFile($filePath, $batchProducts, $exportData = []) {
        F()->log->developer("XML Batch: Добавяне на " . count($batchProducts) . " продукта към: " . $filePath, __FILE__, __LINE__);

        // Проверяваме дали dataModel е зададен
        if (!$this->dataModel) {
            throw new \Exception('DataModel не е инициализиран в Productexportxml');
        }

        // Зареждаме съществуващия XML файл
        $xml = new \DOMDocument('1.0', 'UTF-8');
        $xml->load($filePath);
        $xml->formatOutput = true;

        $root = $xml->getElementsByTagName('products')->item(0);
        if (!$root) {
            throw new \Exception('Невалидна XML структура в файла');
        }

        // Обработваме данните за експорт и определяме избраните полета
        $selectedFields = $this->processExportData($exportData);

        // Извличаме ID-тата на продуктите
        $productIds = array_column($batchProducts, 'product_id');

        // Подготвяме опциите за централизираното извличане на данни
        $options = $this->prepareDataOptions($exportData);

        // Извличаме всички данни централизирано
        $enrichedProducts = $this->dataModel->getProductsForExport($productIds, $options);

        $addedRows = 0;
        // Добавяме продуктите използвайки същия метод като в generateFile()
        foreach ($enrichedProducts as $product) {
            $productElement = $this->createProductElement($xml, $product, $selectedFields);
            $root->appendChild($productElement);
            $addedRows++;
        }

        // Записваме файла
        $xml->save($filePath);

        F()->log->developer("XML Batch: Добавени {$addedRows} продукта", __FILE__, __LINE__);
        return $addedRows;
    }

    /**
     * Финализира batch XML файла (в случая не е необходимо допълнително действие)
     */
    public function finalizeBatchXmlFile($filePath, $exportData = []) {
        F()->log->developer("XML Batch: Финализиране на XML файл: " . $filePath, __FILE__, __LINE__);

        // За XML файловете не е необходимо допълнително действие за финализиране
        // XML структурата е вече завършена

        // Проверяваме дали файлът съществува и е валиден
        if (!file_exists($filePath)) {
            throw new \Exception('XML файлът не съществува: ' . $filePath);
        }

        // Проверяваме дали XML файлът е валиден
        $xml = new \DOMDocument('1.0', 'UTF-8');
        if (!$xml->load($filePath)) {
            throw new \Exception('Невалиден XML файл: ' . $filePath);
        }

        F()->log->developer("XML Batch: XML файлът е финализиран успешно", __FILE__, __LINE__);
        return true;
    }

    /**
     * Генерира данни за продукти за incremental експорт
     */
    public function generateProductData($products, $exportData = []) {
        // Проверяваме дали dataModel е зададен
        if (!$this->dataModel) {
            throw new \Exception('DataModel не е инициализиран в Productexportxml');
        }

        // Включваме debug режима ако е developer
        if (function_exists('isDeveloper') && isDeveloper()) {
            $this->fieldHelper->setDebugMode(true);
        }

        // Обработваме данните за експорт и определяме избраните полета
        $selectedFields = $this->processExportData($exportData);

        // Извличаме ID-тата на продуктите
        $productIds = array_column($products, 'product_id');

        // Подготвяме опциите за централизираното извличане на данни
        $options = $this->prepareDataOptions($exportData);

        // Извличаме всички данни централизирано с batch обработка
        $enrichedProducts = $this->dataModel->getProductsForExport($productIds, $options);

        // Подготвяме данните за всички продукти
        $allProductData = [];

        foreach ($enrichedProducts as $product) {
            $rows = $this->prepareProductRows($product, $selectedFields);
            foreach ($rows as $productRow) {
                $allProductData[] = $productRow;
            }
        }

        return $allProductData;
    }

    /**
     * Подготвя редовете с данни за продукт използвайки предварително извлечени данни
     */
    private function prepareProductRows($product, $selectedFields) {

        $rows = [];

        // Защита срещу липсващи зависимости
        if (!$this->dataModel) {
            throw new \Exception('DataModel не е инициализиран в prepareProductRows');
        }

        // Използваме предварително извлечените описания
        $descriptions = isset($product['product_descriptions']) ? $product['product_descriptions'] : [];

        // Ако имаме само един език или няма описания, създаваме един ред
        if ($this->languageCount <= 1 || empty($descriptions)) {
            $rows[] = $this->prepareProductRow($product, $descriptions, $selectedFields, true);
        } else {
            // Създаваме ред за всеки език
            $rows[] = $this->prepareProductRow($product, $descriptions, $selectedFields, false);
        }
        return $rows;
    }

    /**
     * Подготвя един ред с данни за продукт
     */
    private function prepareProductRow($product, $descriptions, $selectedFields, $singleLanguage = true) {
        $row = [];

        // Защита срещу липсващи зависимости
        if (!$this->dataModel) {
            throw new \Exception('DataModel не е инициализиран в prepareProductRow');
        }

        if (!$this->fieldHelper) {
            throw new \Exception('FieldHelper не е инициализиран в prepareProductRow');
        }

        $availableFields = $this->getAvailableFields();

        // Винаги добавяме product_id като първо поле
        $row[] = $product['product_id'];

        // Винаги добавяме name ако е в избраните полета
        if (in_array('name', $selectedFields)) {
            // Получаваме името от описанията
            $description = null;
            if (!empty($descriptions)) {
                foreach ($descriptions as $desc) {
                    if ($desc['language_id'] == $this->currentLanguageId) {
                        $description = $desc;
                        break;
                    }
                }
                // Ако няма описание за текущия език, използваме първото налично
                if (!$description) {
                    $description = reset($descriptions);
                }
            }
            $row[] = $description['name'] ?? '';
        }

        // Винаги добавяме model ако е в избраните полета
        if (in_array('model', $selectedFields)) {
            $row[] = $product['model'] ?? '';
        }

        // Добавяме основните полета (без product_id, name, model които вече са добавени)
        foreach ($availableFields['basic'] as $field => $label) {
            if (in_array($field, $selectedFields) && !in_array($field, ['product_id', 'name', 'model'])) {
                switch ($field) {
                    case 'model':
                        $row[] = $product['model'] ?? '';
                        break;
                    case 'sku':
                        $row[] = $product['sku'] ?? '';
                        break;
                    case 'upc':
                        $row[] = $product['upc'] ?? '';
                        break;
                    case 'ean':
                        $row[] = $product['ean'] ?? '';
                        break;
                    case 'jan':
                        $row[] = $product['jan'] ?? '';
                        break;
                    case 'isbn':
                        $row[] = $product['isbn'] ?? '';
                        break;
                    case 'mpn':
                        $row[] = $product['mpn'] ?? '';
                        break;
                    case 'location':
                        $row[] = $product['location'] ?? '';
                        break;
                    case 'quantity':
                        $row[] = $this->dataModel->formatQuantity($product['quantity'] ?? 0);
                        break;
                    case 'stock_status':
                        $row[] = $product['stock_status'] ?? '';
                        break;
                    case 'image':
                        $row[] = $product['image_url'] ?? $this->dataModel->getImageUrl($product['image'] ?? '');
                        break;
                    case 'additional_images':
                        $images = $product['additional_images'] ?? [];
                        $row[] = implode(', ', $images);
                        break;
                    case 'manufacturer':
                        $row[] = $product['manufacturer'] ?? '';
                        break;
                    case 'shipping':
                        $row[] = $product['shipping'] ? 'Yes' : 'No';
                        break;
                    case 'price':
                        $row[] = $this->dataModel->formatPrice($product['price'] ?? 0);
                        break;
                    case 'promo_price':
                        $row[] = $this->dataModel->formatPrice($product['promo_price'] ?? 0.00);
                        break;
                    case 'points':
                        $row[] = $product['points'] ?? 0;
                        break;
                    case 'tax_class':
                        $row[] = $product['tax_class'] ?? '';
                        break;
                    case 'date_available':
                        $row[] = $product['date_available'] ?? '';
                        break;
                    case 'weight':
                        $row[] = number_format((float)($product['weight'] ?? 0), 2, '.', '');
                        break;
                    case 'weight_class':
                        $row[] = $product['weight_class'] ?? '';
                        break;
                    case 'length':
                        $row[] = number_format((float)($product['length'] ?? 0), 2, '.', '');
                        break;
                    case 'width':
                        $row[] = number_format((float)($product['width'] ?? 0), 2, '.', '');
                        break;
                    case 'height':
                        $row[] = number_format((float)($product['height'] ?? 0), 2, '.', '');
                        break;
                    case 'length_class':
                        $row[] = $product['length_class'] ?? '';
                        break;
                    case 'status':
                        $row[] = $product['status'] ? 'Enabled' : 'Disabled';
                        break;
                    case 'sort_order':
                        $row[] = $product['sort_order'] ?? 0;
                        break;
                    default:
                        $row[] = $product[$field] ?? '';
                        break;
                }
            }
        }

        // Добавяме многоезичните полета
        if ($singleLanguage) {
            // Един език - използваме текущия език
            $description = null;
            if (!empty($descriptions)) {
                foreach ($descriptions as $desc) {
                    if ($desc['language_id'] == $this->currentLanguageId) {
                        $description = $desc;
                        break;
                    }
                }
                // Ако няма описание за текущия език, използваме първото налично
                if (!$description) {
                    $description = reset($descriptions);
                }
            }

            foreach ($availableFields['multilingual'] as $field => $label) {
                if (in_array($field, $selectedFields) && $field !== 'name') {
                    switch ($field) {
                        case 'description':
                            $row[] = $this->prepareDescriptionForXML($description['description'] ?? '');
                            break;
                        case 'meta_title':
                            $row[] = $description['meta_title'] ?? '';
                            break;
                        case 'meta_description':
                            $row[] = $description['meta_description'] ?? '';
                            break;
                        case 'meta_keyword':
                            $row[] = $description['meta_keyword'] ?? '';
                            break;
                        case 'tag':
                            $row[] = $description['tag'] ?? '';
                            break;
                        default:
                            $row[] = '';
                            break;
                    }
                }
            }
        } else {
            // Повече езици - добавяме всички езици
            foreach ($this->languages as $langCode => $language) {
                $description = null;
                if (!empty($descriptions)) {
                    foreach ($descriptions as $desc) {
                        if ($desc['language_id'] == $language['language_id']) {
                            $description = $desc;
                            break;
                        }
                    }
                }

                foreach ($availableFields['multilingual'] as $field => $label) {
                    if (in_array($field, $selectedFields) && $field !== 'name') {
                        switch ($field) {
                            case 'description':
                                $row[] = $this->prepareDescriptionForXML($description['description'] ?? '');
                                break;
                            case 'meta_title':
                                $row[] = $description['meta_title'] ?? '';
                                break;
                            case 'meta_description':
                                $row[] = $description['meta_description'] ?? '';
                                break;
                            case 'meta_keyword':
                                $row[] = $description['meta_keyword'] ?? '';
                                break;
                            case 'tag':
                                $row[] = $description['tag'] ?? '';
                                break;
                            default:
                                $row[] = '';
                                break;
                        }
                    }
                }
            }
        }

        // Добавяме допълнителните полета
        foreach ($availableFields['additional'] as $field => $label) {
            if (in_array($field, $selectedFields)) {
                switch ($field) {
                    case 'categories':
                        $categories = $product['product_categories'] ?? [];
                        $categoryPaths = [];
                        foreach ($categories as $category) {
                            $path = $category['path'] ?? [$category['category_name'] ?? ''];
                            $categoryPaths[] = implode(' > ', $path);
                        }
                        $row[] = implode(', ', $categoryPaths);
                        break;
                    case 'attributes':
                        $attributes = $product['product_attributes'] ?? [];
                        $attributeStrings = [];
                        foreach ($attributes as $attr) {
                            $attributeStrings[] = ($attr['attribute_name'] ?? '') . ': ' . ($attr['attribute_text'] ?? '');
                        }
                        $row[] = implode(', ', $attributeStrings);
                        break;
                    case 'options':
                        $options = $product['product_options'] ?? [];
                        $optionStrings = [];
                        foreach ($options as $opt) {
                            $optionStrings[] = ($opt['option_name'] ?? '') . ': ' . ($opt['value'] ?? '');
                        }
                        $row[] = implode('; ', $optionStrings);
                        break;
                    default:
                        $row[] = '';
                        break;
                }
            }
        }

        return $row;
    }

    /**
     * Връща всички налични полета за експорт
     */
    public function getAvailableFields() {
        return $this->fieldHelper->getFieldsForExport();
    }

    /**
     * Връща групираните полета за интерфейса
     */
    public function getGroupedFields() {
        return $this->fieldHelper->getAvailableFields();
    }

    /**
     * Връща заглавията на групите
     */
    public function getGroupLabels() {
        return $this->fieldHelper->getGroupLabels();
    }

    /**
     * Връща полетата по подразбиране
     */
    private function getDefaultFields() {
        return $this->fieldHelper->getDefaultFields();
    }

    /**
     * Подготвя опциите за централизираното извличане на данни
     */
    private function prepareDataOptions($exportData) {
        $options = [];

        // Проверяваме основните опции
        if (isset($exportData['basic_options'])) {
            $basicOptions = $exportData['basic_options'];

            $options['include_promo_price'] = !empty($basicOptions['include_promo_price']);
            $options['include_categories'] = !empty($basicOptions['include_categories']);
            $options['include_options'] = !empty($basicOptions['include_options']);
            $options['include_attributes'] = !empty($basicOptions['include_attributes']);
            $options['include_descriptions'] = !empty($basicOptions['include_descriptions']);
        }

        // Проверяваме избраните полета
        if (isset($exportData['selected_fields'])) {
            $selectedFields = $exportData['selected_fields'];

            if (in_array('additional_images', $selectedFields)) {
                $options['include_additional_images'] = true;
            }
        }

        return $options;
    }

    /**
     * Обработва данните за експорт и връща масив с избрани полета
     */
    private function processExportData($exportData) {
        // Ако няма данни за експорт, използваме всички полета по подразбиране
        if (empty($exportData)) {
            return $this->getDefaultFields();
        }

        // Ако данните са директно масив с полета (за обратна съвместимост)
        if (is_array($exportData) && !isset($exportData['type']) && !isset($exportData['basic_options'])) {
            return $exportData;
        }

        // Използваме централизирания метод от helper-а
        return $this->fieldHelper->processSelectedFields($exportData, 'xml');
    }

    /**
     * Инициализира езиците в системата и текущия език
     * МЕТОД ПРЕМАХНАТ - езиците се управляват централизирано от Productexportdata.php
     */
    private function initializeLanguages($languageId = null) {
        // Този метод е премахнат като част от рефакторирането
        // Езиците се управляват централизирано в Productexportdata.php
        // За обратна съвместимост запазваме основната логика

        // Инициализираме езиците от dataModel ако е възможно
        if ($this->dataModel && is_callable([$this->dataModel, 'getLanguages'])) {
            $this->languages = $this->dataModel->getLanguages();
            $this->languageCount = count($this->languages);
        } else {
            // Fallback - използваме основния език
            $this->languages = [
                'bg' => [
                    'language_id' => 1,
                    'name' => 'Bulgarian',
                    'code' => 'bg'
                ]
            ];
            $this->languageCount = 1;
        }

        // Задаваме текущия език
        if ($languageId !== null) {
            $this->currentLanguageId = $languageId;
        } else {
            $this->currentLanguageId = 1; // Fallback
        }
    }

    /**
     * Създава XML елемент за продукт
     */
    private function createProductElement($xml, $product, $selectedFields) {
        $productElement = $xml->createElement('product');
        $availableFields = $this->getAvailableFields();

        // Винаги добавяме product_id като атрибут
        $productElement->setAttribute('id', $product['product_id'] ?? '');

        // Винаги добавяме основните задължителни полета
        $mandatoryFields = ['name', 'model'];
        foreach ($mandatoryFields as $field) {
            if (in_array($field, $selectedFields)) {
                $value = $product[$field] ?? '';
                $element = $xml->createElement($field);
                $element->appendChild($xml->createCDATASection($this->dataModel->cleanText($value)));
                $productElement->appendChild($element);
            }
        }

        // Добавяме основните полета (без product_id, name, model които вече са добавени)
        foreach ($availableFields['basic'] as $field => $label) {
            if (in_array($field, $selectedFields) && !in_array($field, ['product_id', 'name', 'model'])) {
                $value = $product[$field] ?? '';

                // Специална обработка за някои полета използвайки предварително извлечени данни
                if ($field === 'image' && !empty($value)) {
                    $value = $product['image_url'] ?? $this->dataModel->getImageUrl($value);
                } elseif ($field === 'additional_images') {
                    $images = $product['additional_images'] ?? [];
                    $element = $xml->createElement($field);
                    $imagesElement = $xml->createElement('images');
                    foreach ($images as $image) {
                        $imageElement = $xml->createElement('image');
                        $imageElement->appendChild($xml->createCDATASection($image));
                        $imagesElement->appendChild($imageElement);
                    }
                    $element->appendChild($imagesElement);
                    $productElement->appendChild($element);
                    continue; // Пропускаме стандартната обработка
                } elseif ($field === 'quantity') {
                    $value = $this->dataModel->formatQuantity($value);
                } elseif ($field === 'promo_price') {
                    // Използваме предварително извлечената промо цена
                    $value = $this->dataModel->formatPrice($product['promo_price'] ?? 0.00);
                } elseif (in_array($field, ['price', 'weight', 'length', 'width', 'height'])) {
                    $value = $this->dataModel->formatPrice($value);
                }

                $element = $xml->createElement($field);
                $element->appendChild($xml->createCDATASection($this->dataModel->cleanText($value)));
                $productElement->appendChild($element);
            }
        }

        // Добавяме многоезичните полета използвайки предварително извлечени данни
        $descriptions = isset($product['product_descriptions']) ? $product['product_descriptions'] : [];

        if ($this->languageCount <= 1) {
            // Един език - без суфикси
            $currentData = !empty($descriptions) ? reset($descriptions) : [];
            foreach ($availableFields['multilingual'] as $field => $label) {
                if (in_array($field, $selectedFields) && $field !== 'name') {
                    $element = $xml->createElement($field);
                    if ($field === 'description') {
                        // За описанието запазваме HTML но го подготвяме за XML
                        $element->appendChild($xml->createCDATASection($this->prepareDescriptionForXML($currentData[$field] ?? '')));
                    } else {
                        $element->appendChild($xml->createCDATASection($this->dataModel->cleanText($currentData[$field] ?? '')));
                    }
                    $productElement->appendChild($element);
                }
            }
        } else {
            // Повече езици - добавяме елементи със суфикси
            foreach ($this->languages as $langCode => $language) {
                $langData = $descriptions[$langCode] ?? [];
                foreach ($availableFields['multilingual'] as $field => $label) {
                    if (in_array($field, $selectedFields) && $field !== 'name') {
                        $elementName = $field . '_' . $langCode;
                        $element = $xml->createElement($elementName);
                        if ($field === 'description') {
                            // За описанието запазваме HTML но го подготвяме за XML
                            $element->appendChild($xml->createCDATASection($this->prepareDescriptionForXML($langData[$field] ?? '')));
                        } else {
                            $element->appendChild($xml->createCDATASection($this->dataModel->cleanText($langData[$field] ?? '')));
                        }
                        $productElement->appendChild($element);
                    }
                }
            }
        }

        // Добавяме допълнителните полета
        foreach ($availableFields['additional'] as $field => $label) {
            if (in_array($field, $selectedFields)) {
                $element = $xml->createElement($field);

                switch ($field) {
                    case 'categories':
                        $categories = $product['product_categories'] ?? [];
                        $categoriesElement = $xml->createElement('category_list');
                        foreach ($categories as $category) {
                            $categoryElement = $xml->createElement('category');
                            $categoryElement->setAttribute('id', $category['category_id'] ?? '');
                            $path = $category['path'] ?? [$category['category_name'] ?? ''];
                            $categoryElement->appendChild($xml->createCDATASection(implode(' > ', $path)));
                            $categoriesElement->appendChild($categoryElement);
                        }
                        $element->appendChild($categoriesElement);
                        break;
                    case 'attributes':
                        $attributes = $product['product_attributes'] ?? [];
                        $attributesElement = $xml->createElement('attribute_list');
                        foreach ($attributes as $attr) {
                            $attrElement = $xml->createElement('attribute');
                            $attrElement->setAttribute('name', $attr['attribute_name'] ?? '');
                            $attrElement->appendChild($xml->createCDATASection($attr['attribute_text'] ?? ''));
                            $attributesElement->appendChild($attrElement);
                        }
                        $element->appendChild($attributesElement);
                        break;
                    case 'options':
                        $options = $product['product_options'] ?? [];
                        $optionsElement = $xml->createElement('option_list');
                        foreach ($options as $opt) {
                            $optElement = $xml->createElement('option');
                            $optElement->setAttribute('name', $opt['option_name'] ?? '');
                            $optElement->appendChild($xml->createCDATASection($opt['value'] ?? ''));
                            $optionsElement->appendChild($optElement);
                        }
                        $element->appendChild($optionsElement);
                        break;
                    default:
                        $element->appendChild($xml->createCDATASection(''));
                }

                $productElement->appendChild($element);
            }
        }

        return $productElement;
    }

    // SQL методи премахнати - използваме централизирани данни от Productexportdata

    /**
     * Подготвя description полето за XML експорт (използва централизираната функция)
     */
    private function prepareDescriptionForXML($description) {
        if (empty($description)) {
            return '';
        }

        // Използваме централизираната функция за почистване на текст
        return $this->dataModel->cleanText($description);
    }

    // Всички SQL методи премахнати - използваме централизирани данни от Productexportdata
    // cleanText метод ще бъде премахнат - използваме централизирания от Productexportdata
    // cleanText метод премахнат - използваме централизирания от Productexportdata
}
<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class Export extends \Theme25\ControllerSubMethods {

    private $supportedFormats = [];
    private $unavailableFormats = [];
    private $formatRequirements = [];
    private $batchSize = 150;
    private $largeBatchSize = 100; // Намален batch размер за memory optimization
    private $largeExportThreshold = 500; // Праг за големи експорти (намален за по-ранно задействане)
    private $languageMapping = [];
    private $categoryCache = [];
    private $selectedCategories = [];
    private $selectedProducts = [];
    private $exportCacheDir = '';

    public function __construct($main_controller) {
        parent::__construct($main_controller);

        defined('VENDORS_DIR') || define('VENDORS_DIR', DIR_SYSTEM . 'storage/vendor/');

        $this->setRegistry('languageId', $this->getLanguageId());

        // Инициализираме централизирания модел за данни
        $this->loadModelAs('catalog/productexportdata', 'dataModel');

        // Инициализираме централизирания field helper
        $this->loadModelAs('catalog/exportfieldshelper', 'fieldHelper');

        $this->setRegistry('exportFieldHelper', $this->fieldHelper);
        $this->setRegistry('exportDataModel', $this->dataModel);

        ini_set('error_log', DIR_LOGS . '/debug.log');

        // Инициализираме export cache директорията
        $this->exportCacheDir = DIR_CACHE . 'export/';
        $this->initializeExportCacheDir();
    }

    /**
     * Инициализира директорията за export cache файлове
     */
    private function initializeExportCacheDir() {
        if (!is_dir($this->exportCacheDir)) {
            if (!mkdir($this->exportCacheDir, 0755, true)) {
                F()->log->developer('Failed to create export cache directory: ' . $this->exportCacheDir, __FILE__, __LINE__);
                throw new \Exception('Не може да се създаде директория за export cache');
            }
        }

        // Почистваме стари export файлове (по-стари от 24 часа)
        $this->cleanupOldExportFiles();
    }

    /**
     * Почиства стари export JSON файлове
     */
    private function cleanupOldExportFiles() {
        $files = glob($this->exportCacheDir . 'export_*.json');
        $cutoffTime = time() - (24 * 60 * 60); // 24 часа

        foreach ($files as $file) {
            if (filemtime($file) < $cutoffTime) {
                unlink($file);
                F()->log->developer('Cleaned up old export file: ' . basename($file), __FILE__, __LINE__);
            }
        }
    }

    /**
     * Записва export данни в JSON файл
     */
    private function saveExportData($exportId, $data) {
        $filePath = $this->exportCacheDir . 'export_' . $exportId . '.json';

        // Добавяме timestamp за tracking
        $data['last_updated'] = time();
        $data['created_at'] = $data['created_at'] ?? time();

        $jsonData = json_encode($data, JSON_PRETTY_PRINT);

        // File locking за concurrent access
        $fp = fopen($filePath, 'w');
        if ($fp && flock($fp, LOCK_EX)) {
            fwrite($fp, $jsonData);
            flock($fp, LOCK_UN);
            fclose($fp);

            F()->log->developer('Export data saved to: ' . $filePath, __FILE__, __LINE__);
            return true;
        } else {
            F()->log->developer('Failed to save export data to: ' . $filePath, __FILE__, __LINE__);
            if ($fp) fclose($fp);
            return false;
        }
    }

    /**
     * Чете export данни от JSON файл
     */
    private function loadExportData($exportId) {
        $filePath = $this->exportCacheDir . 'export_' . $exportId . '.json';

        if (!file_exists($filePath)) {
            F()->log->developer('Export file not found: ' . $filePath, __FILE__, __LINE__);
            return null;
        }

        // File locking за concurrent access
        $fp = fopen($filePath, 'r');
        if ($fp && flock($fp, LOCK_SH)) {
            $jsonData = fread($fp, filesize($filePath));
            flock($fp, LOCK_UN);
            fclose($fp);

            $data = json_decode($jsonData, true);
            if ($data === null) {
                F()->log->developer('Failed to decode JSON from: ' . $filePath, __FILE__, __LINE__);
                return null;
            }

            F()->log->developer('Export data loaded from: ' . $filePath, __FILE__, __LINE__);
            return $data;
        } else {
            F()->log->developer('Failed to read export data from: ' . $filePath, __FILE__, __LINE__);
            if ($fp) fclose($fp);
            return null;
        }
    }

    /**
     * Изтрива export JSON файл
     */
    private function deleteExportData($exportId) {
        $filePath = $this->exportCacheDir . 'export_' . $exportId . '.json';

        if (file_exists($filePath)) {
            if (unlink($filePath)) {
                F()->log->developer('Export file deleted: ' . $filePath, __FILE__, __LINE__);
                return true;
            } else {
                F()->log->developer('Failed to delete export file: ' . $filePath, __FILE__, __LINE__);
                return false;
            }
        }

        return true; // Файлът не съществува, считаме за успех
    }

    /**
     * Проверява кои формати за експорт са налични
     */
    private function checkAvailableFormats() {
        $this->supportedFormats = [];
        $this->unavailableFormats = [];
        $this->formatRequirements = [];

        // CSV - винаги наличен (вграден в PHP)
        $this->supportedFormats[] = 'csv';
        $this->formatRequirements['csv'] = [
            'name' => 'CSV',
            'description' => 'Comma Separated Values (.csv)',
            'icon' => 'ri-file-text-line',
            'available' => true,
            'reason' => 'Вграден в PHP'
        ];

        // XML - проверка за simplexml разширение
        if (extension_loaded('simplexml')) {
            $this->supportedFormats[] = 'xml';
            $this->formatRequirements['xml'] = [
                'name' => 'XML',
                'description' => 'Extensible Markup Language (.xml)',
                'icon' => 'ri-code-s-slash-line',
                'available' => true,
                'reason' => 'SimpleXML разширение активно'
            ];
        } else {
            $this->unavailableFormats[] = 'xml';
            $this->formatRequirements['xml'] = [
                'name' => 'XML',
                'description' => 'Extensible Markup Language (.xml)',
                'icon' => 'ri-code-s-slash-line',
                'available' => false,
                'reason' => 'Липсва SimpleXML разширение'
            ];
        }

        // XLSX - проверка за PhpSpreadsheet или алтернативни библиотеки
        $xlsxAvailable = false;
        $xlsxReason = '';

        if (file_exists(VENDORS_DIR . 'phpoffice/PhpSpreadsheet/IOFactory.php')) {
            $xlsxAvailable = true;
            $xlsxReason = 'PhpOffice\\PhpSpreadsheet';
        }
        else {
            $xlsxReason = 'Липсва PhpOffice\\PhpSpreadsheet библиотека';
        }

        if ($xlsxAvailable) {
            $this->supportedFormats[] = 'xlsx';
            $this->formatRequirements['xlsx'] = [
                'name' => 'XLSX',
                'description' => 'Microsoft Excel (.xlsx)',
                'icon' => 'ri-file-excel-2-line',
                'available' => true,
                'reason' => $xlsxReason
            ];

            require_once(VENDORS_DIR . 'phpoffice/PhpSpreadsheet/autoload.php');
        } else {
            $this->unavailableFormats[] = 'xlsx';
            $this->formatRequirements['xlsx'] = [
                'name' => 'XLSX',
                'description' => 'Microsoft Excel (.xlsx)',
                'icon' => 'ri-file-excel-2-line',
                'available' => false,
                'reason' => $xlsxReason
            ];
        }

        // Логиране на резултатите
        F()->log->developer('Available export formats: ' . implode(', ', $this->supportedFormats), __FILE__, __LINE__);
        if (!empty($this->unavailableFormats)) {
            F()->log->developer('Unavailable export formats: ' . implode(', ', $this->unavailableFormats), __FILE__, __LINE__);
        }
    }

    /**
     * Инициализира мапинга на езиците
     */
    private function initializeLanguageMapping() {
        $this->loadModelAs('localisation/language', 'languageModel');
        $languages = $this->languageModel->getLanguages();

        foreach ($languages as $language) {
            $this->languageMapping[$language['code']] = $language['language_id'];
        }
    }

    /**
     * Инициализира кеша на категориите
     */
    private function initializeCategoryCache() {
        $this->loadModelAs('catalog/category', 'categoryModel');
        $this->loadCategoryPaths();
    }

    /**
     * Зарежда всички категории с техните пътища в кеша
     */
    private function loadCategoryPaths() {
        $language_id = $this->getLanguageId();

        $sql = "SELECT
                    cp.category_id,
                    GROUP_CONCAT(cd.name ORDER BY cp.level SEPARATOR ' > ') AS path
                FROM `" . DB_PREFIX . "category_path` cp
                LEFT JOIN `" . DB_PREFIX . "category_description` cd ON (cp.path_id = cd.category_id)
                WHERE cd.language_id = '{$language_id}'
                GROUP BY cp.category_id
                ORDER BY cp.category_id";

        $query = $this->db->query($sql);

        foreach ($query->rows as $row) {
            $this->categoryCache[$row['category_id']] = $row['path'];
        }
    }

    /**
     * Основен метод за показване на формата за експорт
     */
    public function execute() {
        // Проверяваме дали има AJAX заявка за запазване на настройки
        if (isset($_POST['action']) && $_POST['action'] === 'save_export_settings') {
            $this->saveExportSettings();
            return;
        }

        $this->setTitle('Експорт на продукти');
        $this->initAdminData();
        $this->checkAvailableFormats();
        $this->initializeLanguageMapping();
        $this->initializeCategoryCache();
        $this->addBackendScriptWithVersion('product-export.js', 'footer');
        $this->addBackendScriptWithVersion('product-export-fields.js', 'footer');
        $this->prepareData();
        $this->renderTemplateWithDataAndOutput('catalog/product_export');
    }

    /**
     * Подготвя данните за формата
     */
    public function prepareData() {
        // Зареждаме запазените настройки
        $savedSettings = $this->loadExportSettings();

        $this->setData([
            'back_url' => $this->getAdminLink('catalog/product'),
            'supported_formats' => $this->supportedFormats,
            'unavailable_formats' => $this->unavailableFormats,
            'format_requirements' => $this->formatRequirements,
            'available_formats' => array_filter($this->formatRequirements, function($format) {
                return $format['available'];
            }),
            'languages' => $this->languageMapping,
            'is_developer' => isDeveloper(),
            'available_fields' => $this->fieldHelper->getAvailableFields(),
            'grouped_fields' => $this->fieldHelper->getAvailableFields(),
            'group_labels' => $this->fieldHelper->getGroupLabels(),
            'selected_fields' => $this->getSelectedFields(),
            'saved_settings' => $savedSettings,
            // 'user_token' => $this->session->data['user_token'] ?? ''
        ]);
    }

    /**
     * Връща избраните полета от сесията или по подразбиране
     */
    private function getSelectedFields() {
        // Проверяваме дали има запазени настройки в сесията
        if (isset($this->session->data['export_selected_fields'])) {
            return $this->session->data['export_selected_fields'];
        }

        // По подразбиране връщаме всички полета
        return $this->fieldHelper->getDefaultFields();
    }

    /**
     * Запазва избраните полета в сесията
     */
    private function saveSelectedFields($selectedFields) {
        $this->session->data['export_selected_fields'] = $selectedFields;
    }

    /**
     * Обработва експорта на продукти
     */
    public function process() {

         $this->checkAvailableFormats();

        try {

            // Валидация на заявката
            if (!$this->validateExportRequest()) {
                throw new \Exception('Невалидна заявка за експорт');
            }

            // Получаване на параметрите за експорт
            $exportFormat = $_POST['export_format'] ?? 'csv';
            $this->selectedCategories = $_POST['selected_categories'] ?? [];
            $this->selectedProducts = $_POST['selected_products'] ?? [];

            // Получаване на опциите за експорт (основни категории)
            $exportOptions = [
                'include_quantity' => isset($_POST['include_quantity']) && $_POST['include_quantity'] == '1',
                'include_price' => isset($_POST['include_price']) && $_POST['include_price'] == '1',
                'include_promo_price' => isset($_POST['include_promo_price']) && $_POST['include_promo_price'] == '1',
                'include_descriptions' => isset($_POST['include_descriptions']) && $_POST['include_descriptions'] == '1',
                'include_categories' => isset($_POST['include_categories']) && $_POST['include_categories'] == '1',
                'include_options' => isset($_POST['include_options']) && $_POST['include_options'] == '1',
                'include_attributes' => isset($_POST['include_attributes']) && $_POST['include_attributes'] == '1'
            ];

            // Получаване и валидация на избраните полета (детайлен контрол)
            $selectedFields = $_POST['selected_fields'] ?? [];

            // Ако има избрани полета, използваме детайлния контрол
            if (!empty($selectedFields)) {
                $selectedFields = $this->fieldHelper->validateSelectedFields($selectedFields);
                // Запазваме избраните полета в сесията
                $this->saveSelectedFields($selectedFields);
                // Комбинираме с основните опции
                $exportData = [
                    'type' => 'detailed',
                    'selected_fields' => $selectedFields,
                    'basic_options' => $exportOptions
                ];
            } else {
                // Използваме основните опции
                $exportData = [
                    'type' => 'basic',
                    'basic_options' => $exportOptions
                ];
            }


            F()->log->developer($exportFormat, __FILE__, __LINE__);
            F()->log->developer($exportData, __FILE__, __LINE__);

            

            // Валидация на формата
            if (!in_array($exportFormat, $this->supportedFormats)) {
                throw new \Exception('Неподдържан формат за експорт: ' . $exportFormat);
            }

            // Зареждане на съответния модел за експорт
            $exportModel = $this->loadExportModel($exportFormat);

            // КРИТИЧНО: Добавяме try-catch за getProductsForExport
            try {
                // Получаване на продуктите за експорт
                $products = $this->getProductsForExport();
            } catch (\Exception $e) {
                error_log("CRITICAL DEBUG: Memory грешка в getProductsForExport: " . $e->getMessage());
                // Ако има memory проблем, автоматично превключваме към large export режим
                if (strpos($e->getMessage(), 'памет') !== false || strpos($e->getMessage(), 'memory') !== false) {
                    F()->log->developer("Export Debug: Memory проблем при getProductsForExport, превключване към large export режим", __FILE__, __LINE__);
                    // Получаваме само ID-тата на продуктите за large export
                    $productIds = $this->getAllProductIds();
                    $products = array_map(function($id) { return ['product_id' => $id]; }, $productIds);
                    error_log("CRITICAL DEBUG: Fallback - използваме " . count($products) . " продукта за large export");
                    $this->initiateLargeExport($products, $exportFormat, $exportData);
                    return;
                } else {
                    throw $e;
                }
            }

            if (empty($products)) {
                throw new \Exception('Няма продукти за експорт с избраните критерии');
            }

            // Debug логване на броя продукти
            $productCount = count($products);
            error_log("CRITICAL DEBUG: Успешно получени {$productCount} продукта");
            F()->log->developer("Export Debug: Общо продукти за експорт: {$productCount}", __FILE__, __LINE__);
            F()->log->developer("Export Debug: Праг за големи експорти: {$this->largeExportThreshold}", __FILE__, __LINE__);
            F()->log->developer("Export Debug: Избрани категории: " . count($this->selectedCategories), __FILE__, __LINE__);
            F()->log->developer("Export Debug: Избрани продукти: " . count($this->selectedProducts), __FILE__, __LINE__);

            // КРИТИЧНО: Добавяме подробно логване на сравнението
            F()->log->developer("Export Debug: Сравнение - productCount ({$productCount}) > largeExportThreshold ({$this->largeExportThreshold}) = " . ($productCount > $this->largeExportThreshold ? 'TRUE' : 'FALSE'), __FILE__, __LINE__);

            // КРИТИЧНО: Добавяме var_dump за директна диагностика
            error_log("CRITICAL DEBUG: Преди проверка - productCount: {$productCount}, threshold: {$this->largeExportThreshold}");

            // ПРИНУДИТЕЛНО ЗАДЕЙСТВАНЕ ЗА ТЕСТВАНЕ
            if ($productCount > $this->largeExportThreshold) { // Временно намаляваме прага за тестване
                error_log("CRITICAL DEBUG: Условието за принудително задействане е TRUE!");
                F()->log->developer("Export Debug: ПРИНУДИТЕЛНО задействане на голям експорт режим за {$productCount} продукта (праг {$this->largeExportThreshold})", __FILE__, __LINE__);
                // За големи експорти връщаме информация за batch обработка
                $this->initiateLargeExport($products, $exportFormat, $exportData);
                return; // ВАЖНО: Излизаме от метода тук
            }

            // Проверяваме дали експортът е голям и трябва да се раздели
            if ($productCount > $this->largeExportThreshold) {
                F()->log->developer("Export Debug: Задействане на голям експорт режим за {$productCount} продукта", __FILE__, __LINE__);
                // За големи експорти връщаме информация за batch обработка
                $this->initiateLargeExport($products, $exportFormat, $exportData);
            } else {
                F()->log->developer("Export Debug: Използване на директен експорт за {$productCount} продукта", __FILE__, __LINE__);

                try {
                    // За малки експорти генерираме файла директно
                    $this->generateAndReturnExportFile($exportModel, $products, $exportFormat, $exportData);
                } catch (\Exception $e) {
                    // Ако има memory проблем, автоматично превключваме към batch режим
                    if (strpos($e->getMessage(), 'памет') !== false || strpos($e->getMessage(), 'memory') !== false) {
                        F()->log->developer("Export Debug: Memory проблем при директен експорт, превключване към batch режим", __FILE__, __LINE__);
                        $this->initiateLargeExport($products, $exportFormat, $exportData);
                    } else {
                        throw $e;
                    }
                }
            }

        } catch (\Exception $e) {
            // При грешка връщаме JSON response
            $json = ['error' => $e->getMessage()];
            F()->log->developer('Export error: ' . $e->getMessage(), __FILE__, __LINE__);
            $this->setJSONResponseOutput($json);
        }
    }

    /**
     * Валидира заявката за експорт
     */
    private function validateExportRequest() {

        if (!isset($_POST['export_format'])) {
            return false;
        }

        $exportFormat = $_POST['export_format'];

        if (!in_array($exportFormat, $this->supportedFormats)) {
            // Проверяваме дали форматът е в недостъпните формати
            if (in_array($exportFormat, $this->unavailableFormats)) {
                $formatInfo = $this->formatRequirements[$exportFormat] ?? null;
                $reason = $formatInfo ? $formatInfo['reason'] : 'Неизвестна причина';
                throw new \Exception("Форматът {$exportFormat} не е наличен в момента. Причина: {$reason}");
            } else {
                $supportedList = implode(', ', array_map('strtoupper', $this->supportedFormats));
                throw new \Exception("Неподдържан файлов формат '{$exportFormat}'. Поддържани формати: {$supportedList}");
            }
        }

        return true;
    }

    /**
     * Зарежда съответния модел за експорт и предава централизираните инстанции
     */
    private function loadExportModel($format) {
        $modelPath = 'catalog/productexport' . strtolower($format);
        $this->loadModelAs($modelPath, 'exportModel');

        F()->log->developer("Export Debug: Зареждане на export модел: {$modelPath}", __FILE__, __LINE__);

        // Предаваме централизираните инстанции към експорт модела
        if (is_callable([$this->exportModel, 'setFieldHelper'])) {
            $this->exportModel->setFieldHelper($this->fieldHelper);
        }

        if (is_callable([$this->exportModel, 'setDataModel'])) {
            // Проверяваме дали dataModel е валиден преди предаване
            if (!$this->dataModel) {
                throw new \Exception('DataModel не е инициализиран в Export контролера');
            }

            // Проверяваме дали dataModel има database connection
            if (!$this->dataModel->checkDatabaseInstance()) {
                throw new \Exception('DataModel няма валидна database connection');
            }

            $this->exportModel->setDataModel($this->dataModel);
        }

        return $this->exportModel;
    }

    /**
     * Получава продуктите за експорт според избраните критерии
     */
    private function getProductsForExport() {
        $products = [];
        $productIds = [];

        F()->log->developer("getProductsForExport Debug: Започване на получаване на продукти", __FILE__, __LINE__);
        F()->log->developer("getProductsForExport Debug: Избрани категории: " . count($this->selectedCategories), __FILE__, __LINE__);
        F()->log->developer("getProductsForExport Debug: Избрани продукти: " . count($this->selectedProducts), __FILE__, __LINE__);

        // Ако са избрани категории, получаваме продуктите от тях
        if (!empty($this->selectedCategories)) {
            F()->log->developer("getProductsForExport Debug: Обработка на избрани категории", __FILE__, __LINE__);
            foreach ($this->selectedCategories as $categoryId) {
                $categoryProducts = $this->getProductsFromCategory($categoryId);
                F()->log->developer("getProductsForExport Debug: Категория {$categoryId} има " . count($categoryProducts) . " продукта", __FILE__, __LINE__);
                foreach ($categoryProducts as $product) {
                    $productIds[] = $product['product_id'];
                }
            }
        }

        // Ако са избрани конкретни продукти, добавяме ги
        if (!empty($this->selectedProducts)) {
            F()->log->developer("getProductsForExport Debug: Добавяне на конкретни продукти", __FILE__, __LINE__);
            $productIds = array_merge($productIds, $this->selectedProducts);
        }

        // Ако няма избрани критерии, експортираме всички продукти
        if (empty($productIds)) {
            F()->log->developer("getProductsForExport Debug: Няма избрани критерии, получаване на всички продукти", __FILE__, __LINE__);
            $productIds = $this->getAllProductIds();
            F()->log->developer("getProductsForExport Debug: getAllProductIds върна " . count($productIds) . " продукта", __FILE__, __LINE__);
        }

        // Премахваме дублиращи се ID-та
        $productIds = array_unique($productIds);
        F()->log->developer("getProductsForExport Debug: След премахване на дублирания: " . count($productIds) . " уникални продукта", __FILE__, __LINE__);

        // Получаваме пълните данни за продуктите
        if (!empty($productIds)) {
            $products = $this->getProductsData($productIds);
            F()->log->developer("getProductsForExport Debug: getProductsData върна " . count($products) . " продукта с пълни данни", __FILE__, __LINE__);
        }

        F()->log->developer("getProductsForExport Debug: Финален резултат: " . count($products) . " продукта", __FILE__, __LINE__);
        return $products;
    }

    /**
     * Получава продуктите от дадена категория (включително подкатегории)
     * МИГРИРАН КЪМ ЦЕНТРАЛИЗИРАНИЯ МОДЕЛ - използва Productexportdata.php
     */
    private function getProductsFromCategory($categoryId) {
        // Уверяваме се че централизираният модел има правилния език
        $this->ensureDataModelLanguage();

        // Използваме централизирания метод за по-добра производителност и преизползваемост
        $productIds = $this->dataModel->getProductsFromCategory($categoryId, false);

        // Конвертираме в същия формат като преди за съвместимост
        return array_map(function($id) {
            return ['product_id' => $id];
        }, $productIds);
    }

    /**
     * Получава всички подкатегории на дадена категория рекурсивно
     * МИГРИРАН КЪМ ЦЕНТРАЛИЗИРАНИЯ МОДЕЛ - използва Productexportdata.php
     */
    private function getAllSubcategories($categoryId) {
        // Уверяваме се че централизираният модел има правилния език
        $this->ensureDataModelLanguage();

        // Използваме централизирания метод за по-добра производителност и преизползваемост
        return $this->dataModel->getAllSubcategories($categoryId);
    }

    /**
     * Получава ID-тата на всички продукти (активни и неактивни)
     * МИГРИРАН КЪМ ЦЕНТРАЛИЗИРАНИЯ МОДЕЛ - използва Productexportdata.php
     */
    private function getAllProductIds() {
        F()->log->developer("getAllProductIds Debug: Започване на получаване на всички продукти", __FILE__, __LINE__);

        // Уверяваме се че централизираният модел има правилния език
        $this->ensureDataModelLanguage();

        // Използваме централизирания метод с опция за включване на неактивни продукти
        $productIds = $this->dataModel->getAllProductIds(true); // true = включва неактивни продукти

        F()->log->developer("getAllProductIds Debug: Получени " . count($productIds) . " продукта от dataModel", __FILE__, __LINE__);

        return $productIds;
    }

    /**
     * Получава пълните данни за продуктите използвайки централизираната архитектура
     */
    private function getProductsData($productIds) {
        if (empty($productIds)) {
            return [];
        }

        // Уверяваме се че централизираният модел има правилния език
        // $this->ensureDataModelLanguage();

        // Подготвяме опциите за централизираното извличане на данни
        $options = [
            'include_promo_price' => true,
            'include_categories' => true,
            'include_options' => true,
            'include_attributes' => true,
            'include_additional_images' => true,
            'include_descriptions' => true
        ];

        // Използваме централизираната архитектура за оптимизирано извличане на данни
        return $this->dataModel->getProductsForExport($productIds, $options);
    }

    /**
     * Уверява се че централизираният модел има правилния език
     */
    private function ensureDataModelLanguage() {
            // $this->dataModel->setLanguageId($this->getLanguageId());
    }

  
    /**
     * Генерира и връща експорт файла директно като binary response
     */
    private function generateAndReturnExportFile($exportModel, $products, $format, $exportData = []) {
        // Генерираме име на файла
        $fileName = 'export_products_' . date('Y-m-d') . '.' . $format;

        F()->log->developer($fileName, __FILE__, __LINE__);

        // Създаваме временен файл
        $tempFilePath = tempnam(sys_get_temp_dir(), 'export_');
        if (!$tempFilePath) {
            throw new \Exception('Не може да се създаде временен файл за експорт');
        }

        F()->log->developer($tempFilePath, __FILE__, __LINE__);

        try {
            // Генерираме файла чрез модела, предавайки текущия език и данните за експорт
            $languageId = $this->getLanguageId();

            F()->log->developer($languageId, __FILE__, __LINE__);


            $exportModel->generateFile($products, $tempFilePath, $languageId, $exportData);

            // Проверяваме дали файлът е създаден успешно
            if (!file_exists($tempFilePath)) {
                throw new \Exception('Генерираният файл не съществува');
            }

            // Четем съдържанието на файла
            $fileContent = file_get_contents($tempFilePath);
            if ($fileContent === false) {
                throw new \Exception('Не може да се прочете генерираният файл');
            }

            // Определяме Content-Type според формата
            $contentType = $this->getContentTypeForFormat($format);

            // Изчистваме всички предишни изходи
            if (ob_get_level()) {
                ob_end_clean();
            }

            // Задаваме HTTP хедърите
            header('Content-Type: ' . $contentType);
            header('Content-Disposition: attachment; filename="' . $fileName . '"');
            header('Content-Length: ' . strlen($fileContent));
            header('Cache-Control: no-cache, no-store, must-revalidate');
            header('Pragma: no-cache');
            header('Expires: 0');

            // Изпращаме binary съдържанието
            echo $fileContent;

            // Debug информация за разработчици
            if (isDeveloper()) {
                F()->log->developer("Export file generated: {$fileName}, size: " . strlen($fileContent) . " bytes", __FILE__, __LINE__);
            }

        } finally {
            // Изтриваме временния файл
            if (file_exists($tempFilePath)) {
                unlink($tempFilePath);
            }
        }

        // Прекратяваме изпълнението след изпращане на файла
        exit;
    }

    /**
     * Определя Content-Type хедъра според формата на файла
     */
    private function getContentTypeForFormat($format) {
        switch (strtolower($format)) {
            case 'csv':
                return 'text/csv; charset=UTF-8';
            case 'xml':
                return 'application/xml; charset=UTF-8';
            case 'xlsx':
                return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            default:
                return 'application/octet-stream';
        }
    }

    /**
     * Autocomplete за категории - използва същата логика като основния Category контролер
     */
    public function autocompleteCategory() {
        $json = [];

        if (isset($_GET['filter_name'])) {
            $this->loadModelAs('catalog/category', 'categoryModel');

            // Определяме лимита от заявката или използваме стандартния
            $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;

            // Ограничаваме максималния лимит за безопасност
            if ($limit > 1000) {
                $limit = 1000;
            }

            $filter_data = [
                'filter_name' => $_GET['filter_name'],
                'sort'        => 'name',
                'order'       => 'ASC',
                'start'       => 0,
                'limit'       => $limit
            ];

            $results = $this->categoryModel->getCategories($filter_data);

            foreach ($results as $result) {
                $json[] = [
                    'category_id' => $result['category_id'],
                    'name' => strip_tags(html_entity_decode($result['name'], ENT_QUOTES, 'UTF-8'))
                ];
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Autocomplete за продукти - използва същата логика като ProductAutocomplete
     */
    public function autocompleteProduct() {
        $json = [];

        $this->loadModelAs('catalog/product', 'productModel');

        $filter_data = [
            'start' => 0,
            'limit' => isset($_GET['limit']) ? (int)$_GET['limit'] : 10
        ];

        if (isset($_GET['filter_name'])) {
            $filter_data['filter_name'] = $_GET['filter_name'];
        }

        $results = $this->productModel->getProducts($filter_data);

        foreach ($results as $result) {
            // Подготвяне на изображението
            $thumb = '';
            if (!empty($result['image'])) {
                $this->loadModelAs('tool/Imageservice', 'imageService');
                $image_details = $this->imageService->getImageDetailsByPath($result['image'], 48, 48);
                $thumb = $image_details['resized_image_url'];
            } else {
                $this->loadModelAs('tool/image', 'imageModel');
                $thumb = $this->imageModel->resize('no_image.png', 48, 48);
            }

            $json[] = [
                'product_id' => $result['product_id'],
                'name' => strip_tags(html_entity_decode($result['name'], ENT_QUOTES, 'UTF-8')),
                'model' => $result['model'],
                'price' => $result['price'],
                'thumb' => $thumb
            ];
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Запазва настройките за експорт в JSON файл
     */
    private function saveExportSettings() {
        try {
            $settings = $_POST['settings'] ?? '';
            if (empty($settings)) {
                throw new \Exception('Няма данни за запазване');
            }

            $settingsData = json_decode($settings, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Невалидни данни за настройки');
            }

            // Добавяме timestamp
            $settingsData['saved_at'] = date('Y-m-d H:i:s');
            $settingsData['user_id'] = $this->user->getId() ?? 0;

            // Определяме пътя до файла с настройки
            $settingsDir = DIR_STORAGE . 'export_settings/';
            if (!is_dir($settingsDir)) {
                mkdir($settingsDir, 0755, true);
            }

            $settingsFile = $settingsDir . 'product_export_settings.json';

            // Запазваме настройките
            $result = file_put_contents($settingsFile, json_encode($settingsData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

            if ($result === false) {
                throw new \Exception('Грешка при запазване на настройките');
            }

            // Връщаме успешен отговор
            $this->setJSONResponseOutput(['success' => true, 'message' => 'Настройките са запазени']);

        } catch (\Exception $e) {
            $this->setJSONResponseOutput(['success' => false, 'error' => $e->getMessage()]);
        }
    }

    /**
     * Зарежда запазените настройки за експорт
     */
    private function loadExportSettings() {
        try {
            $settingsFile = DIR_STORAGE . 'export_settings/product_export_settings.json';

            if (!file_exists($settingsFile)) {
                return $this->getDefaultExportSettings();
            }

            $settingsContent = file_get_contents($settingsFile);
            if ($settingsContent === false) {
                return $this->getDefaultExportSettings();
            }

            $settings = json_decode($settingsContent, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return $this->getDefaultExportSettings();
            }

            return $settings;

        } catch (\Exception $e) {
            return $this->getDefaultExportSettings();
        }
    }

    /**
     * Връща настройките по подразбиране
     */
    private function getDefaultExportSettings() {
        return [
            'format' => 'csv',
            'includeDescriptions' => false,
            'includeCategories' => false,
            'includeOptions' => false,
            'selectedFields' => []
        ];
    }

    /**
     * Инициира голям експорт с incremental single file подход
     */
    private function initiateLargeExport($products, $exportFormat, $exportData) {
        F()->log->developer("Large Export Debug: *** НОВА INCREMENTAL АРХИТЕКТУРА СТАРТИРАНА! ***", __FILE__, __LINE__);
        F()->log->developer("Large Export Debug: Започване на incremental single file експорт", __FILE__, __LINE__);

        // Генерираме уникален export ID
        $exportId = uniqid('export_', true);
        $productIds = array_column($products, 'product_id');

        F()->log->developer("Large Export Debug: Export ID: {$exportId}", __FILE__, __LINE__);
        F()->log->developer("Large Export Debug: Общо продукти: " . count($productIds), __FILE__, __LINE__);
        F()->log->developer("Large Export Debug: Batch размер: {$this->largeBatchSize}", __FILE__, __LINE__);

        // Разделяме продуктите на batch-ове за обработка
        $batches = array_chunk($productIds, $this->largeBatchSize);
        $totalBatches = count($batches);

        F()->log->developer("Large Export Debug: Общо batch-ове: {$totalBatches}", __FILE__, __LINE__);

        // Създаваме директно финалния файл с header
        $finalFileName = $this->createIncrementalExportFile($exportId, $exportFormat, $exportData);

        F()->log->developer("Large Export Debug: Incremental файл създаден: {$finalFileName}", __FILE__, __LINE__);

        // Запазваме информацията за incremental експорта в JSON файл
        $exportDataToSave = [
            'export_id' => $exportId,
            'format' => $exportFormat,
            'export_data' => $exportData,
            'batches' => $batches,
            'total_batches' => $totalBatches,
            'total_products' => count($productIds),
            'batch_size' => $this->largeBatchSize,
            'processed_batches' => 0,
            'processed_products' => 0,
            'final_file_name' => $finalFileName, // Incremental файлът
            'current_row' => 2, // Следващия ред за добавяне (ред 1 е header)
            'started_at' => time(),
            'status' => 'initiated',
            'architecture' => 'incremental_single_file' // Маркираме новата архитектура
        ];

        // Записваме данните в JSON файл
        if (!$this->saveExportData($exportId, $exportDataToSave)) {
            throw new \Exception('Не може да се запишат данните за експорта');
        }

        F()->log->developer("Large Export Debug: Incremental export данни записани в JSON файл", __FILE__, __LINE__);

        // Връщаме JSON response с информация за batch обработката
        $json = [
            'large_export' => true,
            'export_id' => $exportId,
            'total_batches' => $totalBatches,
            'total_products' => count($productIds),
            'batch_size' => $this->largeBatchSize,
            'architecture' => 'incremental_single_file'
        ];

        $this->setJSONResponseOutput($json);
    }

    /**
     * Създава incremental export файл с header ред
     */
    private function createIncrementalExportFile($exportId, $exportFormat, $exportData) {
        $tempDir = DIR_UPLOAD . 'export_temp/';

        // Създаваме директорията ако не съществува
        if (!is_dir($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        $finalFileName = "export_products_{$exportId}." . $exportFormat;
        $finalFilePath = $tempDir . $finalFileName;

        F()->log->developer("Incremental Export: Създаване на файл {$finalFilePath}", __FILE__, __LINE__);

        if ($exportFormat === 'xlsx') {
            $this->createIncrementalXlsxFile($finalFilePath, $exportData);
        } elseif ($exportFormat === 'csv') {
            $this->createIncrementalCsvFile($finalFilePath, $exportData);
        } elseif ($exportFormat === 'xml') {
            $this->createIncrementalXmlFile($finalFilePath, $exportData);
        } else {
            throw new \Exception('Неподдържан формат за incremental експорт: ' . $exportFormat);
        }

        F()->log->developer("Incremental Export: Файл създаден успешно: {$finalFileName}", __FILE__, __LINE__);

        return $finalFileName;
    }

    /**
     * Създава XLSX файл с header за incremental експорт
     */
    private function createIncrementalXlsxFile($filePath, $exportData) {
        // Зареждаме PhpSpreadsheet библиотеката
        defined('VENDORS_DIR') || define('VENDORS_DIR', DIR_SYSTEM . 'storage/vendor/');
        require_once(VENDORS_DIR . 'phpoffice/PhpSpreadsheet/autoload.php');

        // Настройваме PhpSpreadsheet за memory efficiency
        try {
            \PhpOffice\PhpSpreadsheet\Settings::setCache(new \PhpOffice\PhpSpreadsheet\Collection\Memory());
            F()->log->developer('Incremental XLSX: PhpSpreadsheet cache настроен успешно', __FILE__, __LINE__);
        } catch (\Exception $e) {
            F()->log->developer('Incremental XLSX: Cache настройка неуспешна: ' . $e->getMessage(), __FILE__, __LINE__);
        }

        // Създаваме нов spreadsheet
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Генерираме header реда
        $headers = $this->getExportHeaders($exportData);

        // Записваме header реда
        $col = 1;
        foreach ($headers as $header) {
            $sheet->setCellValueByColumnAndRow($col, 1, $header);
            $col++;
        }

        F()->log->developer("Incremental XLSX: Header записан с " . count($headers) . " колони", __FILE__, __LINE__);

        // Записваме файла
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save($filePath);

        // Освобождаваме паметта
        $spreadsheet->disconnectWorksheets();
        unset($spreadsheet);

        F()->log->developer("Incremental XLSX: Файл записан успешно", __FILE__, __LINE__);
    }

    /**
     * Създава CSV файл с header за incremental експорт
     */
    private function createIncrementalCsvFile($filePath, $exportData) {
        $headers = $this->getExportHeaders($exportData);

        $csvFile = fopen($filePath, 'w');
        if (!$csvFile) {
            throw new \Exception('Не може да се създаде CSV файл: ' . $filePath);
        }

        // Записваме header реда
        fputcsv($csvFile, $headers);
        fclose($csvFile);

        F()->log->developer("Incremental CSV: Header записан с " . count($headers) . " колони", __FILE__, __LINE__);
    }

    /**
     * Създава XML файл с header за incremental експорт
     */
    private function createIncrementalXmlFile($filePath, $exportData) {
        $headers = $this->getExportHeaders($exportData);

        // Създаваме основната XML структура
        $xml = new \DOMDocument('1.0', 'UTF-8');
        $xml->formatOutput = true;

        $root = $xml->createElement('products');
        $xml->appendChild($root);

        // Добавяме коментар с header информация
        $headerComment = $xml->createComment('Headers: ' . implode(', ', $headers));
        $root->appendChild($headerComment);

        $xml->save($filePath);

        F()->log->developer("Incremental XML: Основна структура създадена с " . count($headers) . " header полета", __FILE__, __LINE__);
    }

    /**
     * Генерира export headers за incremental файлове
     */
    private function getExportHeaders($exportData) {

        F()->log->developer($exportData, __FILE__, __LINE__);

         $selectedFields = ['product_id', 'name', 'model'];

        // Определяме избраните полета според типа на експорта
        if ($exportData['type'] === 'detailed' && !empty($exportData['selected_fields'])) {
            // Добавяме избраните полета, но без дублиране на основните
            foreach ($exportData['selected_fields'] as $field) {
                if (!in_array($field, $selectedFields)) {
                    $selectedFields[] = $field;
                }
            }
        } else {
            // Добавяме полетата от basic_options ако са зададени
            if (!empty($exportData['basic_options'])) {
                $basicOptions = $exportData['basic_options'];

                if (!empty($basicOptions['include_categories'])) {
                    $selectedFields[] = 'categories';
                }
                if (!empty($basicOptions['include_description'])) {
                    $selectedFields[] = 'description';
                }
                if (!empty($basicOptions['include_images'])) {
                    $selectedFields[] = 'image';
                }
                if (!empty($basicOptions['include_seo'])) {
                    $selectedFields[] = 'meta_title';
                    $selectedFields[] = 'meta_description';
                }
            }
        }

        F()->log->developer($selectedFields, __FILE__, __LINE__);

        // Получаваме езиците за многоезични полета
        $languages = [];
        if (method_exists($this, 'getLanguages')) {
            $languages = $this->getLanguages();
        } else {
            // Fallback - използваме текущия език
            $languageId = $this->getLanguageId();
            $languages = [$languageId => ['name' => 'Български', 'code' => 'bg']];
        }

        // Генерираме headers чрез fieldHelper
        $headers = $this->fieldHelper->generateHeaders($selectedFields, $languages, 'csv');

        F()->log->developer("Export Headers: Генерирани " . count($headers) . " headers за полета: " . implode(', ', $selectedFields), __FILE__, __LINE__);

        return $headers;
    }

    /**
     * Обработва един batch от голям експорт с incremental подход
     */
    public function processBatch() {
        // Memory monitoring в началото на batch обработката
        $memoryStart = memory_get_usage(true);
        $memoryLimit = ini_get('memory_limit');
        $memoryLimitBytes = $this->parseMemoryLimit($memoryLimit);
        $memoryUsagePercent = ($memoryStart / $memoryLimitBytes) * 100;

        F()->log->developer("Batch Memory Monitor: Започване на batch - използвани " . round($memoryStart / 1024 / 1024, 2) . "MB от " . $memoryLimit . " (" . round($memoryUsagePercent, 1) . "%)", __FILE__, __LINE__);

        // Проверяваме дали memory usage е критично преди започване
        if ($memoryUsagePercent > 75) {
            F()->log->developer("Batch Memory Monitor: ПРЕДУПРЕЖДЕНИЕ - Memory usage е " . round($memoryUsagePercent, 1) . "% преди започване на batch", __FILE__, __LINE__);

            // Принудително garbage collection преди започване
            if (function_exists('gc_collect_cycles')) {
                $collected = gc_collect_cycles();
                F()->log->developer("Batch Memory Monitor: Garbage collection освободи {$collected} cycles", __FILE__, __LINE__);

                // Проверяваме отново след cleanup
                $memoryAfterGC = memory_get_usage(true);
                $memoryUsagePercent = ($memoryAfterGC / $memoryLimitBytes) * 100;
                F()->log->developer("Batch Memory Monitor: След garbage collection - " . round($memoryAfterGC / 1024 / 1024, 2) . "MB (" . round($memoryUsagePercent, 1) . "%)", __FILE__, __LINE__);
            }
        }

        F()->log->developer('>>> processBatch', __FILE__, __LINE__);

        try {
            // Проверяваме user token
            $userTokenGet = $this->requestGet('user_token');
            $userTokenSession = $this->getSession('user_token') ?? '';

            F()->log->developer('>>> processBatch > 1', __FILE__, __LINE__);

            if (empty($userTokenGet) || empty($userTokenSession) || $userTokenGet !== $userTokenSession) {
                F()->log->developer('Token validation failed: GET=' . $userTokenGet . ', SESSION=' . $userTokenSession, __FILE__, __LINE__);
                throw new \Exception('Невалиден user token или изтекла сесия');
            }

            F()->log->developer('>>> processBatch > 2', __FILE__, __LINE__);

            // Проверяваме дали потребителят е логнат
            if (!$this->user->isLogged()) {
                F()->log->developer('User not logged in', __FILE__, __LINE__);
                throw new \Exception('Потребителят не е логнат');
            }

            F()->log->developer('>>> processBatch > 3', __FILE__, __LINE__);

            $exportId = $_POST['export_id'] ?? '';
            $batchIndex = (int)($_POST['batch_index'] ?? 0);

            if (empty($exportId)) {
                F()->log->developer('Export ID not provided', __FILE__, __LINE__);
                throw new \Exception('Невалиден експорт ID');
            }

            F()->log->developer('>>> processBatch > 4', __FILE__, __LINE__);

            // Зареждаме export данните от JSON файл вместо сесия
            $exportInfo = $this->loadExportData($exportId);
            if (!$exportInfo) {
                F()->log->developer('Export data not found: ' . $exportId, __FILE__, __LINE__);
                throw new \Exception('Невалиден експорт ID или изтекли данни');
            }

            F()->log->developer('>>> processBatch > 5', __FILE__, __LINE__);

            if ($batchIndex >= $exportInfo['total_batches']) {
                throw new \Exception('Невалиден batch индекс');
            }

            F()->log->developer('>>> processBatch > 6', __FILE__, __LINE__);

            // Получаваме продуктите за текущия batch
            $batchProductIds = $exportInfo['batches'][$batchIndex];
            $batchProducts = $this->getProductsDataByIds($batchProductIds);

            if (empty($batchProducts)) {
                throw new \Exception('Няма продукти в текущия batch');
            }

            F()->log->developer("Incremental Batch: Обработка на batch {$batchIndex} с " . count($batchProducts) . " продукта", __FILE__, __LINE__);

            // Добавяме batch_index в exportInfo за правилно именуване на файловете
            $exportInfo['batch_index'] = $batchIndex;

            // НОВА ЛОГИКА: Добавяме данните директно в incremental файла или създаваме batch файл
            $addedRows = $this->appendBatchToIncrementalFile($batchProducts, $exportInfo);

            F()->log->developer("Incremental Batch: Добавени {$addedRows} реда в incremental файла", __FILE__, __LINE__);

            // Обновяваме информацията за прогреса в JSON файла
            $exportInfo['processed_batches'] = $batchIndex + 1;
            $exportInfo['processed_products'] += count($batchProducts);
            $exportInfo['current_row'] += $addedRows; // Обновяваме текущия ред
            $exportInfo['status'] = 'processing';

            // Записваме обновените данни в JSON файла
            if (!$this->saveExportData($exportId, $exportInfo)) {
                F()->log->developer('Failed to save export progress: ' . $exportId, __FILE__, __LINE__);
                throw new \Exception('Не може да се запише прогресът на експорта');
            }

            // Принудително почистване на паметта
            unset($batchProducts);
            gc_collect_cycles();

            // Проверяваме memory usage и логваме ако е необходимо
            $this->logMemoryUsage("След batch {$batchIndex}");

            // Memory monitoring след обработка на batch
            $memoryEnd = memory_get_usage(true);
            $memoryPeak = memory_get_peak_usage(true);
            $memoryUsed = $memoryEnd - $memoryStart;
            $memoryUsagePercent = ($memoryEnd / $memoryLimitBytes) * 100;

            F()->log->developer("Batch Memory Monitor: Завършване на batch - използвани " . round($memoryEnd / 1024 / 1024, 2) . "MB (" . round($memoryUsagePercent, 1) . "%), peak " . round($memoryPeak / 1024 / 1024, 2) . "MB", __FILE__, __LINE__);
            F()->log->developer("Batch Memory Monitor: Batch използва " . round($memoryUsed / 1024 / 1024, 2) . "MB допълнително", __FILE__, __LINE__);

            $json = [
                'success' => true,
                'batch_index' => $batchIndex,
                'processed_batches' => $batchIndex + 1,
                'total_batches' => $exportInfo['total_batches'],
                'processed_products' => $exportInfo['processed_products'],
                'total_products' => $exportInfo['total_products'],
                'current_row' => $exportInfo['current_row'], // Текущия ред в incremental файла
                'added_rows' => $addedRows, // Броя добавени редове в този batch
                'architecture' => ($exportInfo['format'] === 'xlsx') ? 'multiple_files_merge' : 'incremental_single_file',
                'memory_usage' => $memoryEnd,
                'memory_peak' => $memoryPeak,
                'memory_usage_mb' => round($memoryEnd / 1024 / 1024, 2),
                'memory_usage_percent' => round($memoryUsagePercent, 1)
            ];

            // Финален memory cleanup преди отговор
            unset($batchProducts, $exportInfo);
            if (function_exists('gc_collect_cycles')) {
                $collected = gc_collect_cycles();
                F()->log->developer("Batch Memory Monitor: Финален cleanup освободи {$collected} cycles", __FILE__, __LINE__);
            }

            $this->setJSONResponseOutput($json);

        } catch (\Exception $e) {
            // Memory cleanup при грешка
            unset($batchProducts, $exportInfo);
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }

            $json = ['error' => $e->getMessage()];
            F()->log->developer('Batch processing error: ' . $e->getMessage(), __FILE__, __LINE__);
            $this->setJSONResponseOutput($json);
        }
    }

    /**
     * Добавя batch данни с multiple files архитектура
     */
    private function appendBatchToIncrementalFile($batchProducts, $exportInfo) {
        $tempDir = DIR_UPLOAD . 'export_temp/';
        $format = $exportInfo['format'];
        $batchIndex = $exportInfo['batch_index'] ?? 0;

        // За XLSX използваме multiple files подход заради memory ограниченията
        if ($format === 'xlsx') {
            return $this->createBatchXlsxFile($batchProducts, $exportInfo, $tempDir, $batchIndex);
        } elseif ($format === 'csv') {
            // CSV остава с incremental подход - memory efficient
            $filePath = $tempDir . $exportInfo['final_file_name'];
            return $this->appendBatchToCsvFile($filePath, $batchProducts, $exportInfo);
        } elseif ($format === 'xml') {
            // XML остава с incremental подход
            $filePath = $tempDir . $exportInfo['final_file_name'];
            return $this->appendBatchToXmlFile($filePath, $batchProducts, $exportInfo);
        }

        throw new \Exception('Неподдържан формат за incremental append: ' . $format);
    }

    /**
     * Създава отделен XLSX файл за текущия batch
     */
    private function createBatchXlsxFile($batchProducts, $exportInfo, $tempDir, $batchIndex) {
        // Memory monitoring преди започване
        $memoryBefore = memory_get_usage(true);
        $memoryLimit = ini_get('memory_limit');
        $memoryLimitBytes = $this->parseMemoryLimit($memoryLimit);
        $memoryUsagePercent = ($memoryBefore / $memoryLimitBytes) * 100;

        F()->log->developer("Batch XLSX Create: Започване на batch {$batchIndex} - използвани " . round($memoryBefore / 1024 / 1024, 2) . "MB от " . $memoryLimit . " (" . round($memoryUsagePercent, 1) . "%)", __FILE__, __LINE__);

        // Генерираме име на batch файла
        $batchFileName = 'products_batch_' . str_pad($batchIndex + 1, 3, '0', STR_PAD_LEFT) . '.xlsx';
        $batchFilePath = $tempDir . $batchFileName;

        F()->log->developer("Batch XLSX Create: Създаване на batch файл: {$batchFileName}", __FILE__, __LINE__);

        // Зареждаме export модела
        $format = $exportInfo['format'];
        $exportModel = $this->loadExportModel($format);

        if (!$exportModel) {
            throw new \Exception("Не може да се зареди export модел за формат: {$format}");
        }

        // Генерираме batch файла с пълната функционалност на export модела
        $exportModel->generateFile($batchProducts, $batchFilePath, $exportInfo['language_id'] ?? null, $exportInfo['export_data']);

        // Memory monitoring след създаване
        $memoryAfter = memory_get_usage(true);
        $memoryUsed = $memoryAfter - $memoryBefore;
        F()->log->developer("Batch XLSX Create: Batch файл създаден - използвани " . round($memoryUsed / 1024 / 1024, 2) . "MB допълнително", __FILE__, __LINE__);

        // Cleanup
        unset($exportModel);
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }

        return count($batchProducts); // Връщаме броя обработени продукти
    }

    /**
     * Обединява всички batch XLSX файлове с динамично увеличаване на memory_limit
     */
    private function mergeBatchXlsxFiles($exportInfo) {
        $tempDir = DIR_UPLOAD . 'export_temp/';
        $finalFilePath = $tempDir . $exportInfo['final_file_name'];
        $totalBatches = $exportInfo['total_batches'];

        F()->log->developer("XLSX Dynamic Merge: Започване на merge на {$totalBatches} batch файла с динамично memory управление", __FILE__, __LINE__);

        // Запазваме оригиналния memory limit
        $originalMemoryLimit = ini_get('memory_limit');
        $originalMemoryLimitBytes = $this->parseMemoryLimit($originalMemoryLimit);

        // Memory monitoring преди merge
        $memoryBefore = memory_get_usage(true);
        $memoryUsagePercent = ($memoryBefore / $originalMemoryLimitBytes) * 100;

        F()->log->developer("XLSX Dynamic Merge: Memory преди merge - " . round($memoryBefore / 1024 / 1024, 2) . "MB от " . $originalMemoryLimit . " (" . round($memoryUsagePercent, 1) . "%)", __FILE__, __LINE__);

        // Динамично увеличаване на memory_limit за merge процеса
        $newMemoryLimit = $this->increaseMemoryLimitForMerge($originalMemoryLimit);

        try {
            // Принудително garbage collection преди merge
            if (function_exists('gc_collect_cycles')) {
                $collected = gc_collect_cycles();
                F()->log->developer("XLSX Dynamic Merge: Garbage collection преди merge освободи {$collected} cycles", __FILE__, __LINE__);
            }

            // Използваме streaming merge подход
            $result = $this->streamingMergeViaCsv($exportInfo, $tempDir, $totalBatches);

            F()->log->developer("XLSX Dynamic Merge: Streaming merge завършен успешно", __FILE__, __LINE__);

            return $result;

        } catch (\Exception $e) {
            F()->log->developer("XLSX Dynamic Merge: Грешка по време на merge: " . $e->getMessage(), __FILE__, __LINE__);
            throw $e;

        } finally {
            // Винаги възстановяваме оригиналния memory limit
            $this->restoreOriginalMemoryLimit($originalMemoryLimit);
        }
    }

    /**
     * Streaming merge чрез CSV с enhanced memory monitoring
     */
    private function streamingMergeViaCsv($exportInfo, $tempDir, $totalBatches) {
        $csvTempFile = $tempDir . 'merge_temp.csv';
        $finalFilePath = $tempDir . $exportInfo['final_file_name'];

        // Memory monitoring в началото на streaming merge
        $memoryAtStart = memory_get_usage(true);
        $currentMemoryLimit = ini_get('memory_limit');
        $currentMemoryLimitBytes = $this->parseMemoryLimit($currentMemoryLimit);
        $memoryUsagePercent = ($memoryAtStart / $currentMemoryLimitBytes) * 100;

        F()->log->developer("CSV Streaming Merge: Започване с memory limit: {$currentMemoryLimit}, използвани: " . round($memoryAtStart / 1024 / 1024, 2) . "MB (" . round($memoryUsagePercent, 1) . "%)", __FILE__, __LINE__);
        F()->log->developer("CSV Streaming Merge: Създаване на временен CSV файл: merge_temp.csv", __FILE__, __LINE__);

        try {
            // Стъпка 1: Създаваме временен CSV файл от всички batch XLSX файлове
            F()->log->developer("CSV Streaming Merge: Фаза 1 - Extraction на {$totalBatches} batch файла към CSV", __FILE__, __LINE__);
            $totalMergedRows = $this->extractAllBatchesToCsv($tempDir, $totalBatches, $csvTempFile);

            F()->log->developer("CSV Streaming Merge: Извлечени {$totalMergedRows} реда в CSV файл", __FILE__, __LINE__);

            // Memory monitoring след CSV extraction
            $memoryAfterCsv = memory_get_usage(true);
            $memoryUsedForCsv = $memoryAfterCsv - $memoryAtStart;
            F()->log->developer("CSV Streaming Merge: Memory след CSV extraction - " . round($memoryAfterCsv / 1024 / 1024, 2) . "MB (използвани " . round($memoryUsedForCsv / 1024 / 1024, 2) . "MB за extraction)", __FILE__, __LINE__);

            // Стъпка 2: Конвертираме CSV към XLSX с memory optimization
            F()->log->developer("CSV Streaming Merge: Фаза 2 - Конвертиране на CSV към XLSX", __FILE__, __LINE__);
            $this->convertCsvToXlsxStreaming($csvTempFile, $finalFilePath);

            // Memory monitoring след XLSX conversion
            $memoryAfterXlsx = memory_get_usage(true);
            $memoryUsedForXlsx = $memoryAfterXlsx - $memoryAfterCsv;
            F()->log->developer("CSV Streaming Merge: Memory след XLSX conversion - " . round($memoryAfterXlsx / 1024 / 1024, 2) . "MB (използвани " . round($memoryUsedForXlsx / 1024 / 1024, 2) . "MB за conversion)", __FILE__, __LINE__);

            F()->log->developer("CSV Streaming Merge: CSV конвертиран към XLSX успешно", __FILE__, __LINE__);

            // Стъпка 3: Изчистваме временните файлове
            if (file_exists($csvTempFile)) {
                unlink($csvTempFile);
                F()->log->developer("CSV Streaming Merge: Временен CSV файл изтрит", __FILE__, __LINE__);
            }

            // Изтриваме batch файловете
            F()->log->developer("CSV Streaming Merge: Фаза 3 - Cleanup на {$totalBatches} batch файла", __FILE__, __LINE__);
            $this->cleanupBatchFiles($tempDir, $totalBatches);

            // Финален memory monitoring
            $memoryAtEnd = memory_get_usage(true);
            $totalMemoryUsed = $memoryAtEnd - $memoryAtStart;
            $memoryPeak = memory_get_peak_usage(true);

            F()->log->developer("CSV Streaming Merge: Завършен успешно - общо използвана памет: " . round($totalMemoryUsed / 1024 / 1024, 2) . "MB, peak: " . round($memoryPeak / 1024 / 1024, 2) . "MB", __FILE__, __LINE__);

            return $totalMergedRows;

        } catch (\Exception $e) {
            // Memory monitoring при грешка
            $memoryAtError = memory_get_usage(true);
            F()->log->developer("CSV Streaming Merge: Грешка при memory usage: " . round($memoryAtError / 1024 / 1024, 2) . "MB - " . $e->getMessage(), __FILE__, __LINE__);

            // Cleanup при грешка
            if (file_exists($csvTempFile)) {
                unlink($csvTempFile);
                F()->log->developer("CSV Streaming Merge: Временен CSV файл изтрит след грешка", __FILE__, __LINE__);
            }

            throw $e;
        }
    }

    /**
     * Извлича данни от всички batch XLSX файлове в един CSV файл
     */
    private function extractAllBatchesToCsv($tempDir, $totalBatches, $csvTempFile) {
        // Зареждаме PhpSpreadsheet библиотеката
        defined('VENDORS_DIR') || define('VENDORS_DIR', DIR_SYSTEM . 'storage/vendor/');
        require_once(VENDORS_DIR . 'phpoffice/PhpSpreadsheet/autoload.php');

        // Настройваме PhpSpreadsheet за memory efficiency
        try {
            $cache = new \PhpOffice\PhpSpreadsheet\Collection\Memory();
            \PhpOffice\PhpSpreadsheet\Settings::setCache($cache);
        } catch (\Exception $e) {
            F()->log->developer('CSV Extraction: Cache настройка неуспешна: ' . $e->getMessage(), __FILE__, __LINE__);
        }

        // Отваряме CSV файла за писане
        $csvHandle = fopen($csvTempFile, 'w');
        if (!$csvHandle) {
            throw new \Exception('Не може да се създаде временен CSV файл: ' . $csvTempFile);
        }

        $totalRows = 0;
        $headerWritten = false;

        // Обработваме всеки batch файл поотделно
        for ($batchIndex = 0; $batchIndex < $totalBatches; $batchIndex++) {
            $batchFileName = 'products_batch_' . str_pad($batchIndex + 1, 3, '0', STR_PAD_LEFT) . '.xlsx';
            $batchFilePath = $tempDir . $batchFileName;

            if (!file_exists($batchFilePath)) {
                F()->log->developer("CSV Extraction: Batch файл не съществува: {$batchFileName}", __FILE__, __LINE__);
                continue;
            }

            F()->log->developer("CSV Extraction: Обработка на batch файл: {$batchFileName}", __FILE__, __LINE__);

            // Memory monitoring преди зареждане на batch
            $memoryBeforeBatch = memory_get_usage(true);

            try {
                // Зареждаме batch файла с memory optimization
                $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
                $reader->setReadDataOnly(true);
                $reader->setReadEmptyCells(false);

                $batchSpreadsheet = $reader->load($batchFilePath);
                $batchSheet = $batchSpreadsheet->getActiveSheet();

                // Извличаме данните от batch файла
                $highestRow = $batchSheet->getHighestRow();
                $highestColumn = $batchSheet->getHighestColumn();

                // За първия batch записваме и header-а
                $startRow = (!$headerWritten) ? 1 : 2;

                for ($row = $startRow; $row <= $highestRow; $row++) {
                    $rowData = [];
                    for ($col = 'A'; $col <= $highestColumn; $col++) {
                        $cellValue = $batchSheet->getCell($col . $row)->getValue();
                        $rowData[] = $cellValue;
                    }

                    // Записваме реда в CSV файла
                    fputcsv($csvHandle, $rowData);
                    $totalRows++;

                    if (!$headerWritten) {
                        $headerWritten = true;
                    }
                }

                // Агресивно освобождаване на паметта
                $batchSpreadsheet->disconnectWorksheets();
                unset($batchSpreadsheet, $batchSheet, $reader);

            } catch (\Exception $e) {
                F()->log->developer("CSV Extraction: Грешка при обработка на {$batchFileName}: " . $e->getMessage(), __FILE__, __LINE__);
                continue;
            }

            // Memory cleanup на всеки 5 batch-а
            if (($batchIndex + 1) % 5 === 0) {
                if (function_exists('gc_collect_cycles')) {
                    $collected = gc_collect_cycles();
                    F()->log->developer("CSV Extraction: Garbage collection след batch {$batchIndex} освободи {$collected} cycles", __FILE__, __LINE__);
                }

                $memoryAfterBatch = memory_get_usage(true);
                $memoryUsed = $memoryAfterBatch - $memoryBeforeBatch;
                F()->log->developer("CSV Extraction: Memory след batch {$batchIndex} - " . round($memoryAfterBatch / 1024 / 1024, 2) . "MB (използвани " . round($memoryUsed / 1024 / 1024, 2) . "MB за batch)", __FILE__, __LINE__);
            }
        }

        fclose($csvHandle);

        F()->log->developer("CSV Extraction: Извлечени общо {$totalRows} реда от {$totalBatches} batch файла", __FILE__, __LINE__);

        return $totalRows;
    }

    /**
     * Конвертира CSV файл към XLSX с streaming approach
     */
    private function convertCsvToXlsxStreaming($csvFilePath, $xlsxFilePath) {
        F()->log->developer("CSV to XLSX: Започване на конвертиране от CSV към XLSX", __FILE__, __LINE__);

        // Memory monitoring преди конвертиране
        $memoryBefore = memory_get_usage(true);
        F()->log->developer("CSV to XLSX: Memory преди конвертиране - " . round($memoryBefore / 1024 / 1024, 2) . "MB", __FILE__, __LINE__);

        // Зареждаме PhpSpreadsheet библиотеката
        defined('VENDORS_DIR') || define('VENDORS_DIR', DIR_SYSTEM . 'storage/vendor/');
        require_once(VENDORS_DIR . 'phpoffice/PhpSpreadsheet/autoload.php');

        // Настройваме PhpSpreadsheet за максимална memory efficiency
        try {
            $cache = new \PhpOffice\PhpSpreadsheet\Collection\Memory();
            \PhpOffice\PhpSpreadsheet\Settings::setCache($cache);

            // Изключваме calculation cache
            if (class_exists('\PhpOffice\PhpSpreadsheet\Calculation\Calculation')) {
                \PhpOffice\PhpSpreadsheet\Calculation\Calculation::getInstance()->setCalculationCacheEnabled(false);
            }
        } catch (\Exception $e) {
            F()->log->developer('CSV to XLSX: Cache настройка неуспешна: ' . $e->getMessage(), __FILE__, __LINE__);
        }

        // Създаваме нов spreadsheet
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Products');

        // Отваряме CSV файла за четене
        $csvHandle = fopen($csvFilePath, 'r');
        if (!$csvHandle) {
            throw new \Exception('Не може да се отвори CSV файл за четене: ' . $csvFilePath);
        }

        $currentRow = 1;
        $processedRows = 0;
        $memoryLimit = ini_get('memory_limit');
        $memoryLimitBytes = $this->parseMemoryLimit($memoryLimit);

        // Четем CSV файла ред по ред и записваме в XLSX
        while (($csvRow = fgetcsv($csvHandle)) !== false) {
            $col = 1;
            foreach ($csvRow as $cellValue) {
                $sheet->setCellValueByColumnAndRow($col, $currentRow, $cellValue);
                $col++;
            }
            $currentRow++;
            $processedRows++;

            // Memory monitoring и cleanup на всеки 100 реда
            if ($processedRows % 100 === 0) {
                $currentMemory = memory_get_usage(true);
                $memoryUsagePercent = ($currentMemory / $memoryLimitBytes) * 100;

                if ($memoryUsagePercent > 80) {
                    F()->log->developer("CSV to XLSX: КРИТИЧНО - Memory usage е " . round($memoryUsagePercent, 1) . "% при ред {$currentRow}", __FILE__, __LINE__);

                    // Принудително garbage collection
                    if (function_exists('gc_collect_cycles')) {
                        $collected = gc_collect_cycles();
                        F()->log->developer("CSV to XLSX: Garbage collection освободи {$collected} cycles", __FILE__, __LINE__);
                    }

                    // Проверяваме отново след cleanup
                    $currentMemory = memory_get_usage(true);
                    $memoryUsagePercent = ($currentMemory / $memoryLimitBytes) * 100;

                    if ($memoryUsagePercent > 90) {
                        throw new \Exception("CSV to XLSX: Memory usage достигна " . round($memoryUsagePercent, 1) . "% - спиране за предотвратяване на crash");
                    }
                }

                // Логваме прогреса на всеки 500 реда
                if ($processedRows % 500 === 0) {
                    F()->log->developer("CSV to XLSX: Обработени {$processedRows} реда, memory usage: " . round($currentMemory / 1024 / 1024, 2) . "MB", __FILE__, __LINE__);
                }
            }
        }

        fclose($csvHandle);

        F()->log->developer("CSV to XLSX: Обработени общо {$processedRows} реда", __FILE__, __LINE__);

        // Memory monitoring преди записване
        $memoryBeforeSave = memory_get_usage(true);
        F()->log->developer("CSV to XLSX: Memory преди записване - " . round($memoryBeforeSave / 1024 / 1024, 2) . "MB", __FILE__, __LINE__);

        F()->log->developer("CSV to XLSX: Започване на форматиране на XLSX файла", __FILE__, __LINE__);

        // Прилагаме форматиране на XLSX файла
        $this->applyXlsxFormatting($sheet, $processedRows);

        // Записваме XLSX файла с optimizations
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);

        try {
            if (method_exists($writer, 'setUseDiskCaching')) {
                $writer->setUseDiskCaching(true);
                F()->log->developer("CSV to XLSX: Writer disk caching включен", __FILE__, __LINE__);
            }
            if (method_exists($writer, 'setPreCalculateFormulas')) {
                $writer->setPreCalculateFormulas(false);
                F()->log->developer("CSV to XLSX: Writer pre-calculation изключен", __FILE__, __LINE__);
            }
        } catch (\Exception $e) {
            F()->log->developer('CSV to XLSX: Writer optimization неуспешна: ' . $e->getMessage(), __FILE__, __LINE__);
        }

        F()->log->developer("CSV to XLSX: Записване на форматиран XLSX файл", __FILE__, __LINE__);
        $writer->save($xlsxFilePath);

        // Memory monitoring след записване
        $memoryAfterSave = memory_get_usage(true);
        F()->log->developer("CSV to XLSX: Memory след записване - " . round($memoryAfterSave / 1024 / 1024, 2) . "MB", __FILE__, __LINE__);

        // Агресивно освобождаване на паметта
        $spreadsheet->disconnectWorksheets();
        unset($spreadsheet, $sheet, $writer);

        if (function_exists('gc_collect_cycles')) {
            $collected = gc_collect_cycles();
            F()->log->developer("CSV to XLSX: Финален garbage collection освободи {$collected} cycles", __FILE__, __LINE__);
        }

        // Memory monitoring след cleanup
        $memoryAfterCleanup = memory_get_usage(true);
        F()->log->developer("CSV to XLSX: Memory след cleanup - " . round($memoryAfterCleanup / 1024 / 1024, 2) . "MB", __FILE__, __LINE__);

        F()->log->developer("CSV to XLSX: Конвертиране завършено успешно", __FILE__, __LINE__);
    }

    /**
     * Прилага форматиране на XLSX файла - left alignment и auto-sizing
     */
    private function applyXlsxFormatting($sheet, $totalRows) {
        F()->log->developer("XLSX Formatting: Започване на форматиране на {$totalRows} реда", __FILE__, __LINE__);

        try {
            // Получаваме размерите на данните
            $highestColumn = $sheet->getHighestColumn();
            $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);

            F()->log->developer("XLSX Formatting: Форматиране на {$highestColumnIndex} колони до ред {$totalRows}", __FILE__, __LINE__);

            // Memory monitoring преди форматиране
            $memoryBefore = memory_get_usage(true);
            F()->log->developer("XLSX Formatting: Memory преди форматиране - " . round($memoryBefore / 1024 / 1024, 2) . "MB", __FILE__, __LINE__);

            // Стъпка 1: Прилагаме left alignment на всички клетки
            $this->applyLeftAlignmentToAllCells($sheet, $highestColumnIndex, $totalRows);

            // Memory monitoring след alignment
            $memoryAfterAlignment = memory_get_usage(true);
            F()->log->developer("XLSX Formatting: Memory след alignment - " . round($memoryAfterAlignment / 1024 / 1024, 2) . "MB", __FILE__, __LINE__);

            // Стъпка 2: Прилагаме auto-sizing на всички колони
            $this->applyAutoSizingToAllColumns($sheet, $highestColumnIndex);

            // Memory monitoring след auto-sizing
            $memoryAfterSizing = memory_get_usage(true);
            F()->log->developer("XLSX Formatting: Memory след auto-sizing - " . round($memoryAfterSizing / 1024 / 1024, 2) . "MB", __FILE__, __LINE__);

            F()->log->developer("XLSX Formatting: Форматиране завършено успешно", __FILE__, __LINE__);

        } catch (\Exception $e) {
            F()->log->developer("XLSX Formatting: Грешка при форматиране: " . $e->getMessage(), __FILE__, __LINE__);
            // Не хвърляме exception-а, за да не прекъснем експорта
            // Файлът ще бъде създаден без форматиране
        }
    }

    /**
     * Прилага left alignment на всички клетки в sheet-а
     */
    private function applyLeftAlignmentToAllCells($sheet, $highestColumnIndex, $totalRows) {
        F()->log->developer("XLSX Alignment: Прилагане на left alignment за {$highestColumnIndex} колони и {$totalRows} реда", __FILE__, __LINE__);

        try {
            // Създаваме style за left alignment
            $leftAlignmentStyle = [
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT,
                    'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_TOP,
                    'wrapText' => false
                ]
            ];

            // Определяме range-а за всички данни
            $startCell = 'A1';
            $endCell = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($highestColumnIndex) . $totalRows;
            $range = $startCell . ':' . $endCell;

            F()->log->developer("XLSX Alignment: Прилагане на alignment за range: {$range}", __FILE__, __LINE__);

            // Прилагаме style-а на целия range
            $sheet->getStyle($range)->applyFromArray($leftAlignmentStyle);

            F()->log->developer("XLSX Alignment: Left alignment приложен успешно", __FILE__, __LINE__);

        } catch (\Exception $e) {
            F()->log->developer("XLSX Alignment: Грешка при прилагане на alignment: " . $e->getMessage(), __FILE__, __LINE__);
            throw $e;
        }
    }

    /**
     * Прилага auto-sizing на всички колони
     */
    private function applyAutoSizingToAllColumns($sheet, $highestColumnIndex) {
        F()->log->developer("XLSX Auto-Sizing: Прилагане на auto-sizing за {$highestColumnIndex} колони", __FILE__, __LINE__);

        try {
            // Прилагаме auto-sizing на всяка колона поотделно
            for ($col = 1; $col <= $highestColumnIndex; $col++) {
                $columnLetter = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($col);

                // Прилагаме auto-sizing на колоната
                $sheet->getColumnDimension($columnLetter)->setAutoSize(true);

                // Логваме прогреса на всяка 10-та колона
                if ($col % 10 === 0) {
                    F()->log->developer("XLSX Auto-Sizing: Обработени {$col} от {$highestColumnIndex} колони", __FILE__, __LINE__);
                }
            }

            F()->log->developer("XLSX Auto-Sizing: Auto-sizing приложен успешно на всички {$highestColumnIndex} колони", __FILE__, __LINE__);

        } catch (\Exception $e) {
            F()->log->developer("XLSX Auto-Sizing: Грешка при прилагане на auto-sizing: " . $e->getMessage(), __FILE__, __LINE__);
            throw $e;
        }
    }

    /**
     * Увеличава memory_limit динамично за merge процеса
     */
    private function increaseMemoryLimitForMerge($originalMemoryLimit) {
        F()->log->developer("Memory Management: Започване на динамично увеличаване на memory_limit", __FILE__, __LINE__);

        $originalBytes = $this->parseMemoryLimit($originalMemoryLimit);
        $originalMB = round($originalBytes / 1024 / 1024, 0);

        // Определяме новия memory limit базиран на текущия
        $newMemoryLimitMB = $this->calculateOptimalMemoryLimit($originalMB);
        $newMemoryLimit = $newMemoryLimitMB . 'M';

        F()->log->developer("Memory Management: Опит за увеличаване от {$originalMemoryLimit} ({$originalMB}MB) на {$newMemoryLimit}", __FILE__, __LINE__);

        // Опитваме се да увеличим memory_limit
        $success = ini_set('memory_limit', $newMemoryLimit);

        if ($success !== false) {
            // Проверяваме дали промяната е приложена успешно
            $actualNewLimit = ini_get('memory_limit');
            $actualNewBytes = $this->parseMemoryLimit($actualNewLimit);
            $actualNewMB = round($actualNewBytes / 1024 / 1024, 0);

            if ($actualNewBytes > $originalBytes) {
                F()->log->developer("Memory Management: ✅ Memory limit увеличен успешно от {$originalMB}MB на {$actualNewMB}MB", __FILE__, __LINE__);
                F()->log->developer("Memory Management: Налична допълнителна памет: " . ($actualNewMB - $originalMB) . "MB", __FILE__, __LINE__);
                return $actualNewLimit;
            } else {
                F()->log->developer("Memory Management: ⚠️ Memory limit не се промени - остава {$actualNewMB}MB", __FILE__, __LINE__);
                return $originalMemoryLimit;
            }
        } else {
            F()->log->developer("Memory Management: ❌ Неуспешно увеличаване на memory_limit - ini_set() върна false", __FILE__, __LINE__);
            F()->log->developer("Memory Management: Възможни причини: server ограничения, suhosin extension, или недостатъчни права", __FILE__, __LINE__);
            return $originalMemoryLimit;
        }
    }

    /**
     * Изчислява оптималния memory limit за merge процеса
     */
    private function calculateOptimalMemoryLimit($currentMB) {
        // Логика за определяне на новия memory limit
        if ($currentMB <= 128) {
            // За 128MB или по-малко, увеличаваме на 512MB
            return 512;
        } elseif ($currentMB <= 256) {
            // За 256MB или по-малко, увеличаваме на 1024MB
            return 1024;
        } elseif ($currentMB <= 512) {
            // За 512MB или по-малко, увеличаваме на 1536MB
            return 1536;
        } else {
            // За по-големи стойности, удвояваме
            return $currentMB * 2;
        }
    }

    /**
     * Възстановява оригиналния memory_limit
     */
    private function restoreOriginalMemoryLimit($originalMemoryLimit) {
        F()->log->developer("Memory Management: Възстановяване на оригиналния memory_limit: {$originalMemoryLimit}", __FILE__, __LINE__);

        $currentLimit = ini_get('memory_limit');

        if ($currentLimit !== $originalMemoryLimit) {
            $success = ini_set('memory_limit', $originalMemoryLimit);

            if ($success !== false) {
                $restoredLimit = ini_get('memory_limit');
                if ($restoredLimit === $originalMemoryLimit) {
                    F()->log->developer("Memory Management: ✅ Memory limit възстановен успешно на {$originalMemoryLimit}", __FILE__, __LINE__);
                } else {
                    F()->log->developer("Memory Management: ⚠️ Memory limit частично възстановен - очакван: {$originalMemoryLimit}, действителен: {$restoredLimit}", __FILE__, __LINE__);
                }
            } else {
                F()->log->developer("Memory Management: ❌ Неуспешно възстановяване на memory_limit - ini_set() върна false", __FILE__, __LINE__);
            }
        } else {
            F()->log->developer("Memory Management: Memory limit вече е на оригиналната стойност: {$originalMemoryLimit}", __FILE__, __LINE__);
        }

        // Финален memory monitoring
        $finalMemory = memory_get_usage(true);
        $finalPeak = memory_get_peak_usage(true);
        F()->log->developer("Memory Management: Финална memory статистика - текуща: " . round($finalMemory / 1024 / 1024, 2) . "MB, peak: " . round($finalPeak / 1024 / 1024, 2) . "MB", __FILE__, __LINE__);
    }

    /**
     * Обработва memory exhaustion грешки с fallback опции
     */
    private function handleMemoryExhaustionFallback($exportInfo, $originalException) {
        F()->log->developer("Memory Fallback: Започване на fallback обработка за memory exhaustion", __FILE__, __LINE__);

        $tempDir = DIR_UPLOAD . 'export_temp/';
        $totalBatches = $exportInfo['total_batches'];

        // Проверяваме текущия memory limit
        $currentMemoryLimit = ini_get('memory_limit');
        $currentMemoryLimitBytes = $this->parseMemoryLimit($currentMemoryLimit);
        $currentMemoryLimitMB = round($currentMemoryLimitBytes / 1024 / 1024, 0);

        F()->log->developer("Memory Fallback: Текущ memory limit: {$currentMemoryLimit} ({$currentMemoryLimitMB}MB)", __FILE__, __LINE__);

        // Опция 1: Опитваме се с още по-голям memory limit
        if ($currentMemoryLimitMB < 2048) {
            F()->log->developer("Memory Fallback: Опит за увеличаване на memory limit до 2048MB", __FILE__, __LINE__);

            $extremeMemoryLimit = '2048M';
            $success = ini_set('memory_limit', $extremeMemoryLimit);

            if ($success !== false) {
                $newLimit = ini_get('memory_limit');
                $newLimitBytes = $this->parseMemoryLimit($newLimit);

                if ($newLimitBytes > $currentMemoryLimitBytes) {
                    F()->log->developer("Memory Fallback: ✅ Memory limit увеличен до {$newLimit} - опит за повторен merge", __FILE__, __LINE__);

                    try {
                        // Принудително garbage collection
                        if (function_exists('gc_collect_cycles')) {
                            $collected = gc_collect_cycles();
                            F()->log->developer("Memory Fallback: Garbage collection освободи {$collected} cycles", __FILE__, __LINE__);
                        }

                        // Опитваме се отново с по-голям memory limit
                        $mergedRows = $this->streamingMergeViaCsv($exportInfo, $tempDir, $totalBatches);
                        F()->log->developer("Memory Fallback: ✅ Merge успешен с увеличен memory limit - обединени {$mergedRows} реда", __FILE__, __LINE__);

                        // Валидираме файла
                        $this->validateFinalExportFile($exportInfo);

                        return; // Успешно завършване

                    } catch (\Exception $e) {
                        F()->log->developer("Memory Fallback: ❌ Merge неуспешен дори с увеличен memory limit: " . $e->getMessage(), __FILE__, __LINE__);
                    }
                }
            }
        }

        // Опция 2: Предлагаме CSV експорт като алтернатива
        F()->log->developer("Memory Fallback: Предлагане на CSV експорт като алтернатива", __FILE__, __LINE__);

        try {
            $csvFileName = str_replace('.xlsx', '.csv', $exportInfo['final_file_name']);
            $csvFilePath = $tempDir . $csvFileName;

            // Създаваме CSV файл от batch файловете
            $totalMergedRows = $this->extractAllBatchesToCsv($tempDir, $totalBatches, $csvFilePath);

            F()->log->developer("Memory Fallback: ✅ CSV файл създаден успешно с {$totalMergedRows} реда", __FILE__, __LINE__);

            // Обновяваме export info за CSV формат
            $exportInfo['format'] = 'csv';
            $exportInfo['final_file_name'] = $csvFileName;
            $exportInfo['fallback_reason'] = 'memory_exhaustion_xlsx_merge';
            $exportInfo['original_format'] = 'xlsx';

            // Изтриваме batch файловете
            $this->cleanupBatchFiles($tempDir, $totalBatches);

            F()->log->developer("Memory Fallback: ✅ Fallback към CSV формат завършен успешно", __FILE__, __LINE__);

            return; // Успешно завършване с CSV

        } catch (\Exception $csvException) {
            F()->log->developer("Memory Fallback: ❌ CSV fallback също неуспешен: " . $csvException->getMessage(), __FILE__, __LINE__);
        }

        // Опция 3: Предоставяме batch файловете като ZIP архив
        F()->log->developer("Memory Fallback: Предлагане на ZIP архив с batch файлове", __FILE__, __LINE__);

        try {
            $zipFileName = str_replace('.xlsx', '_batches.zip', $exportInfo['final_file_name']);
            $zipFilePath = $tempDir . $zipFileName;

            $this->createBatchFilesZip($tempDir, $totalBatches, $zipFilePath);

            // Обновяваме export info за ZIP формат
            $exportInfo['format'] = 'zip';
            $exportInfo['final_file_name'] = $zipFileName;
            $exportInfo['fallback_reason'] = 'memory_exhaustion_all_formats';
            $exportInfo['original_format'] = 'xlsx';
            $exportInfo['zip_contents'] = 'individual_xlsx_batch_files';

            F()->log->developer("Memory Fallback: ✅ ZIP архив създаден успешно", __FILE__, __LINE__);

            return; // Успешно завършване с ZIP

        } catch (\Exception $zipException) {
            F()->log->developer("Memory Fallback: ❌ ZIP fallback също неуспешен: " . $zipException->getMessage(), __FILE__, __LINE__);
        }

        // Ако всички fallback опции се провалят, хвърляме оригиналната грешка
        F()->log->developer("Memory Fallback: ❌ Всички fallback опции неуспешни - хвърляне на оригиналната грешка", __FILE__, __LINE__);
        throw new \Exception("Memory exhaustion при XLSX merge. Оригинална грешка: " . $originalException->getMessage() . ". Всички fallback опции също се провалиха.");
    }

    /**
     * Създава ZIP архив с всички batch файлове
     */
    private function createBatchFilesZip($tempDir, $totalBatches, $zipFilePath) {
        if (!class_exists('ZipArchive')) {
            throw new \Exception('ZipArchive класът не е наличен на сървъра');
        }

        $zip = new \ZipArchive();
        $result = $zip->open($zipFilePath, \ZipArchive::CREATE | \ZipArchive::OVERWRITE);

        if ($result !== TRUE) {
            throw new \Exception('Не може да се създаде ZIP файл: ' . $result);
        }

        $addedFiles = 0;

        for ($batchIndex = 0; $batchIndex < $totalBatches; $batchIndex++) {
            $batchFileName = 'products_batch_' . str_pad($batchIndex + 1, 3, '0', STR_PAD_LEFT) . '.xlsx';
            $batchFilePath = $tempDir . $batchFileName;

            if (file_exists($batchFilePath)) {
                $zip->addFile($batchFilePath, $batchFileName);
                $addedFiles++;
            }
        }

        // Добавяме README файл с инструкции
        $readmeContent = "XLSX Export Batch Files\n\n";
        $readmeContent .= "Този архив съдържа {$addedFiles} XLSX файла с експортирани продукти.\n";
        $readmeContent .= "Всеки файл съдържа част от общия експорт.\n\n";
        $readmeContent .= "За да обедините файловете:\n";
        $readmeContent .= "1. Отворете първия файл (products_batch_001.xlsx)\n";
        $readmeContent .= "2. Копирайте данните от останалите файлове (без header реда)\n";
        $readmeContent .= "3. Поставете ги в първия файл\n\n";
        $readmeContent .= "Причина за разделяне: Memory exhaustion при автоматично обединяване\n";
        $readmeContent .= "Дата на създаване: " . date('Y-m-d H:i:s') . "\n";

        $zip->addFromString('README.txt', $readmeContent);

        $zip->close();

        F()->log->developer("ZIP Creation: Създаден ZIP архив с {$addedFiles} batch файла", __FILE__, __LINE__);
    }

    /**
     * Изтрива batch файловете след успешно обединяване
     */
    private function cleanupBatchFiles($tempDir, $totalBatches) {
        F()->log->developer("XLSX Cleanup: Изтриване на {$totalBatches} batch файла", __FILE__, __LINE__);

        $deletedFiles = 0;
        for ($batchIndex = 0; $batchIndex < $totalBatches; $batchIndex++) {
            $batchFileName = 'products_batch_' . str_pad($batchIndex + 1, 3, '0', STR_PAD_LEFT) . '.xlsx';
            $batchFilePath = $tempDir . $batchFileName;

            if (file_exists($batchFilePath)) {
                if (unlink($batchFilePath)) {
                    $deletedFiles++;
                } else {
                    F()->log->developer("XLSX Cleanup: Неуспешно изтриване на файл: {$batchFileName}", __FILE__, __LINE__);
                }
            }
        }

        F()->log->developer("XLSX Cleanup: Изтрити {$deletedFiles} от {$totalBatches} batch файла", __FILE__, __LINE__);
    }

    /**
     * Валидира финалния обединен експорт файл
     */
    private function validateFinalExportFile($exportInfo) {
        $tempDir = DIR_UPLOAD . 'export_temp/';
        $filePath = $tempDir . $exportInfo['final_file_name'];

        if (!file_exists($filePath)) {
            throw new \Exception('Финалният експорт файл не съществува: ' . $exportInfo['final_file_name']);
        }

        $fileSize = filesize($filePath);
        if ($fileSize === false || $fileSize === 0) {
            throw new \Exception('Финалният експорт файл е празен или повреден');
        }

        F()->log->developer("Export Validation: Финален файл валидиран - размер: " . round($fileSize / 1024 / 1024, 2) . "MB", __FILE__, __LINE__);

        // За XLSX файлове правим допълнителна валидация
        if ($exportInfo['format'] === 'xlsx') {
            try {
                // Зареждаме PhpSpreadsheet библиотеката
                defined('VENDORS_DIR') || define('VENDORS_DIR', DIR_SYSTEM . 'storage/vendor/');
                require_once(VENDORS_DIR . 'phpoffice/PhpSpreadsheet/autoload.php');

                $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
                $reader->setReadDataOnly(true);

                // Опитваме се да заредим файла за валидация
                $spreadsheet = $reader->load($filePath);
                $sheet = $spreadsheet->getActiveSheet();

                $highestRow = $sheet->getHighestRow();
                $highestColumn = $sheet->getHighestColumn();

                F()->log->developer("XLSX Validation: Файлът съдържа {$highestRow} реда и {$highestColumn} колони", __FILE__, __LINE__);

                // Cleanup
                $spreadsheet->disconnectWorksheets();
                unset($spreadsheet, $sheet);

            } catch (\Exception $e) {
                throw new \Exception('XLSX файлът е повреден или нечетим: ' . $e->getMessage());
            }
        }
    }

    /**
     * Парсира memory limit стринг към bytes
     */
    private function parseMemoryLimit($memoryLimit) {
        if ($memoryLimit == -1) {
            return PHP_INT_MAX; // Unlimited
        }

        $unit = strtolower(substr($memoryLimit, -1));
        $value = (int) substr($memoryLimit, 0, -1);

        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return (int) $memoryLimit;
        }
    }

    /**
     * Добавя batch данни в XLSX файл с memory optimization
     */
    private function appendBatchToXlsxFile($filePath, $batchProducts, $exportInfo) {
        // Memory monitoring преди започване
        $memoryBefore = memory_get_usage(true);
        $memoryLimit = ini_get('memory_limit');
        $memoryLimitBytes = $this->parseMemoryLimit($memoryLimit);
        $memoryUsagePercent = ($memoryBefore / $memoryLimitBytes) * 100;

        F()->log->developer("Memory Monitor: Преди XLSX append - използвани " . round($memoryBefore / 1024 / 1024, 2) . "MB от " . $memoryLimit . " (" . round($memoryUsagePercent, 1) . "%)", __FILE__, __LINE__);

        // Проверяваме дали memory usage е над 80% - спираме ако е критично
        if ($memoryUsagePercent > 80) {
            throw new \Exception("Memory usage е " . round($memoryUsagePercent, 1) . "% - спиране за предотвратяване на exhaustion");
        }

        // Зареждаме PhpSpreadsheet библиотеката
        defined('VENDORS_DIR') || define('VENDORS_DIR', DIR_SYSTEM . 'storage/vendor/');
        require_once(VENDORS_DIR . 'phpoffice/PhpSpreadsheet/autoload.php');

        // Настройваме PhpSpreadsheet за максимална memory efficiency
        try {
            // Използваме Memory cache - най-ефективният за малки до средни файлове
            $cache = new \PhpOffice\PhpSpreadsheet\Collection\Memory();
            \PhpOffice\PhpSpreadsheet\Settings::setCache($cache);
            F()->log->developer('Incremental XLSX Append: PhpSpreadsheet Memory cache настроен успешно', __FILE__, __LINE__);

            // Допълнителни memory настройки
            if (class_exists('\PhpOffice\PhpSpreadsheet\Calculation\Calculation')) {
                // Изключваме автоматичните изчисления за по-малко memory usage
                \PhpOffice\PhpSpreadsheet\Calculation\Calculation::getInstance()->setCalculationCacheEnabled(false);
                F()->log->developer('Incremental XLSX Append: Calculation cache изключен за memory efficiency', __FILE__, __LINE__);
            }
        } catch (\Exception $e) {
            F()->log->developer('Incremental XLSX Append: Cache настройка неуспешна: ' . $e->getMessage(), __FILE__, __LINE__);
        }

        // Настройваме допълнителни memory optimizations (съвместими с текущата PhpSpreadsheet версия)
        try {
            // Проверяваме дали има други memory optimization методи
            if (method_exists('\PhpOffice\PhpSpreadsheet\Settings', 'setLibXmlLoaderOptions')) {
                // Настройваме libxml за по-малко memory usage
                \PhpOffice\PhpSpreadsheet\Settings::setLibXmlLoaderOptions(LIBXML_COMPACT);
                F()->log->developer('Incremental XLSX Append: LibXML COMPACT настроен за memory efficiency', __FILE__, __LINE__);
            }
        } catch (\Exception $e) {
            F()->log->developer('Incremental XLSX Append: LibXML настройка неуспешна: ' . $e->getMessage(), __FILE__, __LINE__);
        }

        // Зареждаме съществуващия файл с максимална memory optimization
        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();

        // Настройваме reader за memory efficiency
        $reader->setReadDataOnly(false); // Трябва да можем да записваме
        $reader->setReadEmptyCells(false); // Не четем празни клетки

        // Допълнителни reader optimizations
        try {
            if (method_exists($reader, 'setLoadSheetsOnly')) {
                // Зареждаме само активния sheet
                $reader->setLoadSheetsOnly(['Worksheet']);
                F()->log->developer('Incremental XLSX Append: Reader настроен да зарежда само активния sheet', __FILE__, __LINE__);
            }

            if (method_exists($reader, 'setReadFilter')) {
                // Можем да добавим filter за по-ефективно четене ако е нужно
                F()->log->developer('Incremental XLSX Append: Reader поддържа ReadFilter за memory optimization', __FILE__, __LINE__);
            }
        } catch (\Exception $e) {
            F()->log->developer('Incremental XLSX Append: Reader optimization неуспешна: ' . $e->getMessage(), __FILE__, __LINE__);
        }

        F()->log->developer('Incremental XLSX Append: Зареждане на съществуващ файл: ' . $filePath, __FILE__, __LINE__);
        $spreadsheet = $reader->load($filePath);
        $sheet = $spreadsheet->getActiveSheet();

        // Memory monitoring след зареждане на файла
        $memoryAfterLoad = memory_get_usage(true);
        F()->log->developer("Memory Monitor: След зареждане на XLSX файл - " . round($memoryAfterLoad / 1024 / 1024, 2) . "MB", __FILE__, __LINE__);

        $currentRow = $exportInfo['current_row'];
        $addedRows = 0;

        // Зареждаме export модела за текущия формат
        $format = $exportInfo['format'];
        $exportModel = $this->loadExportModel($format);

        if (!$exportModel) {
            throw new \Exception("Не може да се зареди export модел за формат: {$format}");
        }

        // Debugging: Логваме информация за export модела
        F()->log->developer("Incremental XLSX Append: Export модел тип: " . get_class($exportModel), __FILE__, __LINE__);
        F()->log->developer("Incremental XLSX Append: Export модел методи: " . implode(', ', get_class_methods($exportModel)), __FILE__, __LINE__);

        // Проверяваме дали методът generateProductData съществува
        if (!is_callable([$exportModel, 'generateProductData'])) {
            throw new \Exception("Export модела за формат {$format} няма метод generateProductData()");
        }

        // Генерираме данните за продуктите
        F()->log->developer("Incremental XLSX Append: Извикване на generateProductData() с " . count($batchProducts) . " продукта", __FILE__, __LINE__);
        $productData = $exportModel->generateProductData($batchProducts, $exportInfo['export_data']);

        F()->log->developer("Incremental XLSX Append: Генерирани данни за " . count($productData) . " продукта", __FILE__, __LINE__);

        // Memory monitoring преди добавяне на данни
        $memoryBeforeData = memory_get_usage(true);
        F()->log->developer("Memory Monitor: Преди добавяне на данни - " . round($memoryBeforeData / 1024 / 1024, 2) . "MB", __FILE__, __LINE__);

        // Добавяме всеки продукт като нов ред с memory optimization
        $processedRows = 0;
        foreach ($productData as $productIndex => $product) {
            $col = 1;
            foreach ($product as $value) {
                $sheet->setCellValueByColumnAndRow($col, $currentRow, $value);
                $col++;
            }
            $currentRow++;
            $addedRows++;
            $processedRows++;

            // Освобождаваме memory на всеки 10 реда
            if ($processedRows % 10 === 0) {
                unset($productData[$productIndex]);

                // Проверяваме memory usage на всеки 25 реда
                if ($processedRows % 25 === 0) {
                    $currentMemory = memory_get_usage(true);
                    $memoryUsagePercent = ($currentMemory / $memoryLimitBytes) * 100;

                    if ($memoryUsagePercent > 85) {
                        F()->log->developer("Memory Monitor: КРИТИЧНО - " . round($memoryUsagePercent, 1) . "% memory usage при ред {$currentRow}", __FILE__, __LINE__);

                        // Принудително garbage collection
                        if (function_exists('gc_collect_cycles')) {
                            $collected = gc_collect_cycles();
                            F()->log->developer("Memory Monitor: Garbage collection освободи {$collected} cycles", __FILE__, __LINE__);
                        }

                        // Проверяваме отново след cleanup
                        $currentMemory = memory_get_usage(true);
                        $memoryUsagePercent = ($currentMemory / $memoryLimitBytes) * 100;

                        if ($memoryUsagePercent > 90) {
                            throw new \Exception("Memory usage достигна " . round($memoryUsagePercent, 1) . "% - спиране за предотвратяване на crash");
                        }
                    }
                }
            }
        }

        // Финален memory cleanup
        unset($productData);
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }

        F()->log->developer("Incremental XLSX Append: Добавени {$addedRows} реда, следващ ред: {$currentRow}", __FILE__, __LINE__);

        // Memory monitoring преди записване
        $memoryBeforeSave = memory_get_usage(true);
        F()->log->developer("Memory Monitor: Преди записване на файл - " . round($memoryBeforeSave / 1024 / 1024, 2) . "MB", __FILE__, __LINE__);

        // Записваме файла с memory optimization
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);

        // Настройваме writer за memory efficiency
        try {
            if (method_exists($writer, 'setUseDiskCaching')) {
                // Използваме disk caching за по-малко memory usage
                $writer->setUseDiskCaching(true);
                F()->log->developer('Incremental XLSX Append: Writer disk caching включен', __FILE__, __LINE__);
            }

            if (method_exists($writer, 'setPreCalculateFormulas')) {
                // Изключваме pre-calculation на формули за по-бързо записване
                $writer->setPreCalculateFormulas(false);
                F()->log->developer('Incremental XLSX Append: Writer pre-calculation изключен', __FILE__, __LINE__);
            }
        } catch (\Exception $e) {
            F()->log->developer('Incremental XLSX Append: Writer optimization неуспешна: ' . $e->getMessage(), __FILE__, __LINE__);
        }

        $writer->save($filePath);

        // Memory monitoring след записване
        $memoryAfterSave = memory_get_usage(true);
        F()->log->developer("Memory Monitor: След записване на файл - " . round($memoryAfterSave / 1024 / 1024, 2) . "MB", __FILE__, __LINE__);

        // Агресивно освобождаване на паметта
        $spreadsheet->disconnectWorksheets();
        unset($spreadsheet, $sheet, $writer, $reader, $exportModel);

        // Финален garbage collection
        if (function_exists('gc_collect_cycles')) {
            $collected = gc_collect_cycles();
            F()->log->developer("Memory Monitor: Финален garbage collection освободи {$collected} cycles", __FILE__, __LINE__);
        }

        // Memory monitoring след cleanup
        $memoryAfterCleanup = memory_get_usage(true);
        $memoryFreed = $memoryBefore - $memoryAfterCleanup;
        F()->log->developer("Memory Monitor: След cleanup - " . round($memoryAfterCleanup / 1024 / 1024, 2) . "MB (освободени " . round($memoryFreed / 1024 / 1024, 2) . "MB)", __FILE__, __LINE__);

        return $addedRows;
    }

    /**
     * Добавя batch данни в CSV файл
     */
    private function appendBatchToCsvFile($filePath, $batchProducts, $exportInfo) {
        // Отваряме файла за добавяне
        $csvFile = fopen($filePath, 'a');
        if (!$csvFile) {
            throw new \Exception('Не може да се отвори CSV файл за добавяне: ' . $filePath);
        }

        // Зареждаме export модела за текущия формат
        $format = $exportInfo['format'];
        $exportModel = $this->loadExportModel($format);

        if (!$exportModel) {
            throw new \Exception("Не може да се зареди export модел за формат: {$format}");
        }

        // Проверяваме дали методът generateProductData съществува
        if (!is_callable([$exportModel, 'generateProductData'])) {
            throw new \Exception("Export модела за формат {$format} няма метод generateProductData()");
        }

        // Генерираме данните за продуктите
        F()->log->developer("Incremental CSV Append: Извикване на generateProductData() с " . count($batchProducts) . " продукта", __FILE__, __LINE__);
        $productData = $exportModel->generateProductData($batchProducts, $exportInfo['export_data']);

        $addedRows = 0;
        foreach ($productData as $product) {
            fputcsv($csvFile, $product);
            $addedRows++;
        }

        fclose($csvFile);

        F()->log->developer("Incremental CSV Append: Добавени {$addedRows} реда", __FILE__, __LINE__);
        return $addedRows;
    }

    /**
     * Добавя batch данни в XML файл
     */
    private function appendBatchToXmlFile($filePath, $batchProducts, $exportInfo) {
        // Зареждаме съществуващия XML файл
        $xml = new \DOMDocument('1.0', 'UTF-8');
        $xml->load($filePath);
        $xml->formatOutput = true;

        $root = $xml->getElementsByTagName('products')->item(0);
        if (!$root) {
            throw new \Exception('Невалидна XML структура в файла');
        }

        // Зареждаме export модела за текущия формат
        $format = $exportInfo['format'];
        $exportModel = $this->loadExportModel($format);

        if (!$exportModel) {
            throw new \Exception("Не може да се зареди export модел за формат: {$format}");
        }

        // Проверяваме дали методът generateProductData съществува
        if (!is_callable([$exportModel, 'generateProductData'])) {
            throw new \Exception("Export модела за формат {$format} няма метод generateProductData()");
        }

        // Генерираме данните за продуктите
        F()->log->developer("Incremental XML Append: Извикване на generateProductData() с " . count($batchProducts) . " продукта", __FILE__, __LINE__);
        $productData = $exportModel->generateProductData($batchProducts, $exportInfo['export_data']);
        $headers = $this->getExportHeaders($exportInfo['export_data']);

        $addedRows = 0;
        foreach ($productData as $product) {
            $productElement = $xml->createElement('product');

            for ($i = 0; $i < count($headers); $i++) {
                $fieldElement = $xml->createElement($headers[$i], htmlspecialchars($product[$i] ?? ''));
                $productElement->appendChild($fieldElement);
            }

            $root->appendChild($productElement);
            $addedRows++;
        }

        $xml->save($filePath);

        F()->log->developer("Incremental XML Append: Добавени {$addedRows} реда", __FILE__, __LINE__);
        return $addedRows;
    }

    /**
     * Финализира incremental експорта (вместо комбиниране на файлове)
     */
    public function finalizeLargeExport() {
        try {
            $exportId = $_POST['export_id'] ?? '';

            if (empty($exportId)) {
                throw new \Exception('Невалиден експорт ID');
            }

            // Зареждаме export данните от JSON файл
            $exportInfo = $this->loadExportData($exportId);
            if (!$exportInfo) {
                F()->log->developer('Export data not found for finalization: ' . $exportId, __FILE__, __LINE__);
                throw new \Exception('Невалиден експорт ID или изтекли данни');
            }

            // Проверяваме дали всички batch-ове са обработени
            if ($exportInfo['processed_batches'] < $exportInfo['total_batches']) {
                throw new \Exception('Не всички batch-ове са обработени');
            }

            // Обновяваме статуса на експорта
            $exportInfo['status'] = 'finalizing';
            $this->saveExportData($exportId, $exportInfo);

            F()->log->developer("Export Finalize: Финализиране на експорт с формат: " . $exportInfo['format'], __FILE__, __LINE__);

            $finalFileName = $exportInfo['final_file_name'];

            // За XLSX формат обединяваме batch файловете с динамично memory управление
            if ($exportInfo['format'] === 'xlsx') {
                F()->log->developer("XLSX Finalize: Започване на merge процес за XLSX файлове с динамично memory управление", __FILE__, __LINE__);

                try {
                    // Опитваме се да направим merge с динамично увеличаване на memory_limit
                    $mergedRows = $this->mergeBatchXlsxFiles($exportInfo);
                    F()->log->developer("XLSX Finalize: Merge завършен успешно - обединени {$mergedRows} реда", __FILE__, __LINE__);

                    // Валидираме обединения файл
                    $this->validateFinalExportFile($exportInfo);

                } catch (\Exception $e) {
                    F()->log->developer("XLSX Finalize: Грешка при merge процес: " . $e->getMessage(), __FILE__, __LINE__);

                    // Проверяваме дали грешката е свързана с memory
                    if (strpos($e->getMessage(), 'memory') !== false || strpos($e->getMessage(), 'Memory') !== false) {
                        F()->log->developer("XLSX Finalize: Засечена memory-related грешка - предлагаме fallback решения", __FILE__, __LINE__);

                        // Предлагаме fallback опции
                        $this->handleMemoryExhaustionFallback($exportInfo, $e);
                    } else {
                        // За други грешки, препредаваме exception-а
                        throw $e;
                    }
                }
            } else {
                // За CSV и XML форматите файлът вече е готов от incremental процеса
                F()->log->developer("Export Finalize: Incremental файл готов за формат: " . $exportInfo['format'], __FILE__, __LINE__);

                // Валидираме incremental файла
                $this->validateIncrementalExportFile($exportInfo);
            }

            // Обновяваме статуса на експорта
            $exportInfo['status'] = 'completed';
            $exportInfo['final_file'] = $finalFileName;
            $exportInfo['completed_at'] = time();
            $this->saveExportData($exportId, $exportInfo);

            F()->log->developer("Incremental Finalize: Експорт завършен успешно с файл: {$finalFileName}", __FILE__, __LINE__);

            // Връщаме финалния файл
            $this->returnFinalExportFile($finalFileName, $exportInfo['format']);

            // Изчистваме JSON файла след успешно завършване
            $this->deleteExportData($exportId);

        } catch (\Exception $e) {
            // При грешка обновяваме статуса
            if (!empty($exportId)) {
                $exportInfo = $this->loadExportData($exportId);
                if ($exportInfo) {
                    $exportInfo['status'] = 'error';
                    $exportInfo['error'] = $e->getMessage();
                    $exportInfo['error_at'] = time();
                    $this->saveExportData($exportId, $exportInfo);
                }
            }

            $json = ['error' => $e->getMessage()];
            F()->log->developer('Large export finalization error: ' . $e->getMessage(), __FILE__, __LINE__);
            $this->setJSONResponseOutput($json);
        }
    }

    /**
     * Валидира incremental export файла
     */
    private function validateIncrementalExportFile($exportInfo) {
        $tempDir = DIR_UPLOAD . 'export_temp/';
        $filePath = $tempDir . $exportInfo['final_file_name'];
        $format = $exportInfo['format'];

        if (!file_exists($filePath)) {
            throw new \Exception('Incremental export файлът не съществува: ' . $filePath);
        }

        F()->log->developer("Incremental Validate: Валидиране на {$format} файл", __FILE__, __LINE__);

        if ($format === 'xlsx') {
            $this->validateIncrementalXlsxFile($filePath, $exportInfo);
        } elseif ($format === 'csv') {
            $this->validateIncrementalCsvFile($filePath, $exportInfo);
        } elseif ($format === 'xml') {
            $this->validateIncrementalXmlFile($filePath, $exportInfo);
        }

        F()->log->developer("Incremental Validate: Файл валидиран успешно", __FILE__, __LINE__);
    }

    /**
     * Валидира XLSX incremental файл
     */
    private function validateIncrementalXlsxFile($filePath, $exportInfo) {
        // Зареждаме PhpSpreadsheet библиотеката
        defined('VENDORS_DIR') || define('VENDORS_DIR', DIR_SYSTEM . 'storage/vendor/');
        require_once(VENDORS_DIR . 'phpoffice/PhpSpreadsheet/autoload.php');

        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
        $spreadsheet = $reader->load($filePath);
        $sheet = $spreadsheet->getActiveSheet();

        $totalRows = $sheet->getHighestRow();
        $totalColumns = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($sheet->getHighestColumn());

        // Проверяваме дали има правилен брой редове (1 header + data rows)
        $expectedRows = 1 + $exportInfo['processed_products']; // 1 header + всички продукти

        F()->log->developer("Incremental XLSX Validate: Файл има {$totalRows} реда, очаквани {$expectedRows} реда, {$totalColumns} колони", __FILE__, __LINE__);

        // Проверяваме header реда
        $headerValue = $sheet->getCellByColumnAndRow(1, 1)->getValue();
        if (empty($headerValue)) {
            throw new \Exception('Header редът е празен в XLSX файла');
        }

        // Проверяваме дали има данни
        if ($totalRows < 2) {
            throw new \Exception('XLSX файлът няма данни редове');
        }

        $spreadsheet->disconnectWorksheets();
        unset($spreadsheet);

        F()->log->developer("Incremental XLSX Validate: Валидацията премина успешно", __FILE__, __LINE__);
    }

    /**
     * Валидира CSV incremental файл
     */
    private function validateIncrementalCsvFile($filePath, $exportInfo) {
        $csvFile = fopen($filePath, 'r');
        if (!$csvFile) {
            throw new \Exception('Не може да се отвори CSV файл за валидация: ' . $filePath);
        }

        $rowCount = 0;
        while (($row = fgetcsv($csvFile)) !== false) {
            $rowCount++;
        }
        fclose($csvFile);

        $expectedRows = 1 + $exportInfo['processed_products']; // 1 header + всички продукти

        F()->log->developer("Incremental CSV Validate: Файл има {$rowCount} реда, очаквани {$expectedRows} реда", __FILE__, __LINE__);

        if ($rowCount < 2) {
            throw new \Exception('CSV файлът няма данни редове');
        }

        F()->log->developer("Incremental CSV Validate: Валидацията премина успешно", __FILE__, __LINE__);
    }

    /**
     * Валидира XML incremental файл
     */
    private function validateIncrementalXmlFile($filePath, $exportInfo) {
        $xml = new \DOMDocument('1.0', 'UTF-8');
        if (!$xml->load($filePath)) {
            throw new \Exception('Невалиден XML файл: ' . $filePath);
        }

        $products = $xml->getElementsByTagName('product');
        $productCount = $products->length;
        $expectedProducts = $exportInfo['processed_products'];

        F()->log->developer("Incremental XML Validate: Файл има {$productCount} продукта, очаквани {$expectedProducts} продукта", __FILE__, __LINE__);

        if ($productCount === 0) {
            throw new \Exception('XML файлът няма продукти');
        }

        F()->log->developer("Incremental XML Validate: Валидацията премина успешно", __FILE__, __LINE__);
    }

    /**
     * Проверява статуса на export операция
     */
    public function checkExportStatus() {
        try {
            $exportId = $this->requestGet('export_id') ?? '';

            if (empty($exportId)) {
                throw new \Exception('Невалиден експорт ID');
            }

            // Зареждаме export данните от JSON файл
            $exportInfo = $this->loadExportData($exportId);
            if (!$exportInfo) {
                throw new \Exception('Експортът не е намерен');
            }

            // Връщаме статуса
            $json = [
                'success' => true,
                'export_id' => $exportId,
                'status' => $exportInfo['status'],
                'processed_batches' => $exportInfo['processed_batches'],
                'total_batches' => $exportInfo['total_batches'],
                'processed_products' => $exportInfo['processed_products'],
                'total_products' => $exportInfo['total_products'],
                'started_at' => $exportInfo['started_at'],
                'last_updated' => $exportInfo['last_updated']
            ];

            if (isset($exportInfo['error'])) {
                $json['error'] = $exportInfo['error'];
                $json['error_at'] = $exportInfo['error_at'];
            }

            if (isset($exportInfo['completed_at'])) {
                $json['completed_at'] = $exportInfo['completed_at'];
            }

            $this->setJSONResponseOutput($json);

        } catch (\Exception $e) {
            $json = ['error' => $e->getMessage()];
            F()->log->developer('Export status check error: ' . $e->getMessage(), __FILE__, __LINE__);
            $this->setJSONResponseOutput($json);
        }
    }

    /**
     * Получава данни за продукти по ID-та (за batch обработка)
     */
    private function getProductsDataByIds($productIds) {
        if (empty($productIds)) {
            return [];
        }

        // Уверяваме се че централизираният модел има правилния език
        $this->ensureDataModelLanguage();

        // Задаваме по-малкия batch размер за големи експорти
        $this->dataModel->setBatchSize($this->largeBatchSize);

        // Подготвяме опциите за централизираното извличане на данни
        $options = [
            'include_categories' => true,
            'include_options' => true,
            'include_attributes' => true,
            'include_images' => true,
            'include_descriptions' => true,
            'include_promotional_prices' => true
        ];

        // Използваме централизирания метод за извличане на данни
        return $this->dataModel->getProductsForExport($productIds, $options);
    }

    /**
     * Генерира временен файл за един batch
     */
    private function generateBatchFile($products, $format, $exportData, $exportId, $batchIndex) {
        // Създаваме директорията за временни файлове ако не съществува
        $tempDir = DIR_UPLOAD . 'export_temp/';
        if (!is_dir($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        // Генерираме име на временния файл
        $tempFileName = "batch_{$exportId}_{$batchIndex}.{$format}";
        $tempFilePath = $tempDir . $tempFileName;

        // Зареждаме съответния модел за експорт
        $exportModel = $this->loadExportModel($format);

        try {
            // Генерираме файла чрез модела
            $languageId = $this->getLanguageId();
            $exportModel->generateFile($products, $tempFilePath, $languageId, $exportData);

            if (!file_exists($tempFilePath)) {
                throw new \Exception('Временният файл не беше създаден');
            }

            return $tempFileName;

        } catch (\Exception $e) {
            F()->log->developer('Batch file generation error: ' . $e->getMessage(), __FILE__, __LINE__);
            throw $e;
        }
    }

    /**
     * Обединява всички batch файлове в един финален файл
     */
    private function combineBatchFiles($tempFiles, $format, $exportId) {
        $tempDir = DIR_UPLOAD . 'export_temp/';
        $finalFileName = "export_products_{$exportId}." . $format;
        $finalFilePath = $tempDir . $finalFileName;

        F()->log->developer('Combining batch files: ' . $finalFilePath, __FILE__, __LINE__);

        if ($format === 'csv') {
            return $this->combineCsvFiles($tempFiles, $finalFilePath, $tempDir);
        } elseif ($format === 'xlsx') {
            return $this->combineXlsxFiles($tempFiles, $finalFilePath, $tempDir);
        } elseif ($format === 'xml') {
            return $this->combineXmlFiles($tempFiles, $finalFilePath, $tempDir);
        }

        throw new \Exception('Неподдържан формат за обединяване: ' . $format);
    }

    /**
     * Обединява CSV файлове
     */
    private function combineCsvFiles($tempFiles, $finalFilePath, $tempDir) {
        $finalFile = fopen($finalFilePath, 'w');
        if (!$finalFile) {
            throw new \Exception('Не може да се създаде финалният CSV файл');
        }

        // Добавяме UTF-8 BOM
        fwrite($finalFile, "\xEF\xBB\xBF");

        $headerWritten = false;

        foreach ($tempFiles as $tempFileName) {
            $tempFilePath = $tempDir . $tempFileName;
            if (!file_exists($tempFilePath)) {
                continue;
            }

            $tempFile = fopen($tempFilePath, 'r');
            if (!$tempFile) {
                continue;
            }

            // Пропускаме BOM ако съществува
            $bom = fread($tempFile, 3);
            if ($bom !== "\xEF\xBB\xBF") {
                rewind($tempFile);
            }

            $lineNumber = 0;
            while (($line = fgets($tempFile)) !== false) {
                $lineNumber++;

                // За първия файл записваме заглавието, за останалите го пропускаме
                if ($lineNumber === 1) {
                    if (!$headerWritten) {
                        fwrite($finalFile, $line);
                        $headerWritten = true;
                    }
                } else {
                    fwrite($finalFile, $line);
                }
            }

            fclose($tempFile);
        }

        fclose($finalFile);
        return basename($finalFilePath);
    }

    /**
     * Обединява XLSX файлове от всички batch-ове в един финален файл
     */
    private function combineXlsxFiles($tempFiles, $finalFilePath, $tempDir) {
        if (empty($tempFiles)) {
            throw new \Exception('Няма временни XLSX файлове за обединяване');
        }

        defined('VENDORS_DIR') || define('VENDORS_DIR', DIR_SYSTEM . 'storage/vendor/');
        require_once(VENDORS_DIR . 'phpoffice/PhpSpreadsheet/autoload.php');

        // КРИТИЧНО: Увеличаваме memory limit временно за XLSX обединяване
        $originalMemoryLimit = ini_get('memory_limit');
        $requiredMemory = max(512, count($tempFiles) * 8); // Минимум 512MB или 8MB на файл
        ini_set('memory_limit', $requiredMemory . 'M');
        F()->log->developer('XLSX Combine: Memory limit увеличен от ' . $originalMemoryLimit . ' на ' . $requiredMemory . 'M', __FILE__, __LINE__);

        F()->log->developer('XLSX Combine: Започване на обединяване на ' . count($tempFiles) . ' файла', __FILE__, __LINE__);

        // Проверяваме дали PhpSpreadsheet е наличен
        if (!class_exists('\PhpOffice\PhpSpreadsheet\Spreadsheet')) {
            throw new \Exception('PhpSpreadsheet библиотеката не е налична за обединяване на XLSX файлове');
        }

        // Настройваме PhpSpreadsheet за memory efficiency
        try {
            // Използваме стандартния Memory cache за по-ефективно използване на памет
            \PhpOffice\PhpSpreadsheet\Settings::setCache(new \PhpOffice\PhpSpreadsheet\Collection\Memory());
            F()->log->developer('XLSX Combine: PhpSpreadsheet cache настроен успешно', __FILE__, __LINE__);

            // Допълнителни memory optimization настройки
            if (method_exists('\PhpOffice\PhpSpreadsheet\Settings', 'setLibXmlLoaderOptions')) {
                \PhpOffice\PhpSpreadsheet\Settings::setLibXmlLoaderOptions(LIBXML_COMPACT);
            }

        } catch (\Exception $e) {
            F()->log->developer('XLSX Combine: Не може да се настрои cache: ' . $e->getMessage(), __FILE__, __LINE__);
            // Продължаваме без cache настройка - не е критично
        }

        // Създаваме нов spreadsheet за финалния файл
        $finalSpreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $finalSheet = $finalSpreadsheet->getActiveSheet();
        $finalSheet->setTitle('Products Export');

        $currentRow = 1;
        $headers = null;
        $totalProcessedRows = 0;

        // Обработваме файловете в по-малки групи за memory efficiency
        $chunkSize = 10; // Обработваме по 10 файла наведнъж
        $fileChunks = array_chunk($tempFiles, $chunkSize);

        F()->log->developer('XLSX Combine: Разделяне на ' . count($tempFiles) . ' файла в ' . count($fileChunks) . ' групи от по ' . $chunkSize, __FILE__, __LINE__);

        foreach ($fileChunks as $chunkIndex => $fileChunk) {
            F()->log->developer('XLSX Combine: Обработка на група ' . ($chunkIndex + 1) . '/' . count($fileChunks), __FILE__, __LINE__);

            foreach ($fileChunk as $index => $tempFileName) {
                $globalIndex = ($chunkIndex * $chunkSize) + $index;
                $tempFilePath = $tempDir . $tempFileName;

                if (!file_exists($tempFilePath)) {
                    F()->log->developer('XLSX Combine: Пропускане на липсващ файл: ' . $tempFileName, __FILE__, __LINE__);
                    continue;
                }

                F()->log->developer('XLSX Combine: Обработка на файл ' . ($globalIndex + 1) . '/' . count($tempFiles) . ': ' . $tempFileName, __FILE__, __LINE__);

                // Memory management - логваме текущото използване на памет
                $memoryUsage = memory_get_usage(true);
                $memoryPeak = memory_get_peak_usage(true);
                F()->log->developer('XLSX Combine: Memory usage преди файл ' . ($globalIndex + 1) . ': ' . round($memoryUsage / 1024 / 1024, 2) . 'MB (peak: ' . round($memoryPeak / 1024 / 1024, 2) . 'MB)', __FILE__, __LINE__);

            try {
                // Зареждаме временния XLSX файл
                $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
                $tempSpreadsheet = $reader->load($tempFilePath);
                $tempSheet = $tempSpreadsheet->getActiveSheet();

                // Получаваме максималните редове и колони
                $highestRow = $tempSheet->getHighestRow();
                $highestColumn = $tempSheet->getHighestColumn();
                $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);

                F()->log->developer('XLSX Combine: Файл ' . $tempFileName . ' има ' . $highestRow . ' реда и ' . $highestColumnIndex . ' колони', __FILE__, __LINE__);

                // За първия файл копираме заглавията и установяваме стандартния брой колони
                if ($index === 0) {
                    // Копираме header реда
                    for ($col = 1; $col <= $highestColumnIndex; $col++) {
                        $cellValue = $tempSheet->getCellByColumnAndRow($col, 1)->getValue();
                        $finalSheet->setCellValueByColumnAndRow($col, $currentRow, $cellValue);
                    }
                    $headers = $highestColumnIndex; // Установяваме стандартния брой колони
                    $currentRow++;
                    F()->log->developer('XLSX Combine: Header установен с ' . $headers . ' колони', __FILE__, __LINE__);

                    // Копираме данните (започваме от ред 2)
                    $startRow = 2;
                } else {
                    // За останалите файлове пропускаме header реда
                    $startRow = 2;

                    // ВАЖНО: Проверяваме дали броят колони съвпада със стандарта
                    if ($highestColumnIndex !== $headers) {
                        F()->log->developer('XLSX Combine: ВНИМАНИЕ! Файл ' . $tempFileName . ' има ' . $highestColumnIndex . ' колони, но стандартът е ' . $headers . ' колони', __FILE__, __LINE__);
                        // Използваме по-малкия брой колони за да избегнем грешки
                        $maxColumns = min($highestColumnIndex, $headers);
                    } else {
                        $maxColumns = $headers;
                    }
                }

                // Копираме данните от временния файл с правилен column alignment
                for ($row = $startRow; $row <= $highestRow; $row++) {
                    // Използваме стандартния брой колони за consistency
                    $columnsToProcess = ($index === 0) ? $headers : (isset($maxColumns) ? $maxColumns : $headers);

                    for ($col = 1; $col <= $columnsToProcess; $col++) {
                        $cellValue = $tempSheet->getCellByColumnAndRow($col, $row)->getValue();
                        $finalSheet->setCellValueByColumnAndRow($col, $currentRow, $cellValue);
                    }
                    $currentRow++;
                    $totalProcessedRows++;
                }

                // Освобождаваме паметта
                $tempSpreadsheet->disconnectWorksheets();
                unset($tempSpreadsheet);

                // Принудително почистване на паметта
                gc_collect_cycles();

                // Data integrity validation
                $dataRowsAdded = ($index === 0) ? ($highestRow - 1) : ($highestRow - 1); // Пропускаме header за всички файлове
                $expectedCurrentRow = ($index === 0) ? (2 + $dataRowsAdded) : ($currentRow); // За първия файл: 1 header + data rows

                F()->log->developer('XLSX Combine: Файл ' . $tempFileName . ' обработен успешно. Добавени ' . $dataRowsAdded . ' реда данни. Текущ ред: ' . ($currentRow - 1), __FILE__, __LINE__);

                // Проверяваме data integrity
                if ($index === 0) {
                    F()->log->developer('XLSX Combine: Първи файл - Header + ' . $dataRowsAdded . ' data rows. Следващ ред: ' . $currentRow, __FILE__, __LINE__);
                } else {
                    F()->log->developer('XLSX Combine: Файл ' . ($index + 1) . ' - ' . $dataRowsAdded . ' data rows добавени. Общо data rows: ' . ($currentRow - 2), __FILE__, __LINE__);
                }

                // Memory management - логваме използването след обработка
                $memoryUsageAfter = memory_get_usage(true);
                F()->log->developer('XLSX Combine: Memory usage след файл ' . ($index + 1) . ': ' . round($memoryUsageAfter / 1024 / 1024, 2) . 'MB', __FILE__, __LINE__);

            } catch (\Exception $e) {
                F()->log->developer('XLSX Combine: Грешка при обработка на файл ' . $tempFileName . ': ' . $e->getMessage(), __FILE__, __LINE__);

                // Ако грешката е свързана с памет, превключваме към CSV подход
                if (strpos($e->getMessage(), 'memory') !== false || strpos($e->getMessage(), 'exhausted') !== false) {
                    F()->log->developer('XLSX Combine: Memory грешка в основния loop, превключване към CSV подход', __FILE__, __LINE__);

                    // Cleanup на текущия spreadsheet
                    if (isset($finalSpreadsheet)) {
                        $finalSpreadsheet->disconnectWorksheets();
                        unset($finalSpreadsheet);
                    }
                    gc_collect_cycles();

                    // Възстановяваме memory limit
                    ini_set('memory_limit', $originalMemoryLimit);

                    // Опитваме алтернативния подход
                    return $this->combineXlsxFilesViaCsv($tempFiles, $finalFilePath, $tempDir);
                }

                throw new \Exception('Грешка при четене на временен XLSX файл: ' . $tempFileName . ' - ' . $e->getMessage());
            }
            }

            // Memory cleanup след всяка група файлове
            gc_collect_cycles();
            F()->log->developer('XLSX Combine: Група ' . ($chunkIndex + 1) . ' завършена. Memory cleanup извършен.', __FILE__, __LINE__);
        }

        // Автоматично оразмеряване на колоните
        if ($headers) {
            for ($col = 1; $col <= $headers; $col++) {
                $finalSheet->getColumnDimensionByColumn($col)->setAutoSize(true);
            }
        }

        F()->log->developer('XLSX Combine: Общо обработени ' . $totalProcessedRows . ' реда данни от ' . count($tempFiles) . ' файла', __FILE__, __LINE__);

        // Записваме финалния файл
        try {
            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($finalSpreadsheet);
            $writer->save($finalFilePath);

            // Освобождаваме паметта
            $finalSpreadsheet->disconnectWorksheets();
            unset($finalSpreadsheet);

            // Финална валидация на структурата на файла
            $finalValidationReader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
            $finalValidationSpreadsheet = $finalValidationReader->load($finalFilePath);
            $finalValidationSheet = $finalValidationSpreadsheet->getActiveSheet();

            $finalTotalRows = $finalValidationSheet->getHighestRow();
            $finalTotalColumns = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($finalValidationSheet->getHighestColumn());

            // Проверяваме header реда
            $headerRow1Value = $finalValidationSheet->getCellByColumnAndRow(1, 1)->getValue();
            $isHeaderValid = !empty($headerRow1Value) && (strpos($headerRow1Value, 'ID') !== false || strpos($headerRow1Value, 'Код') !== false);

            // Проверяваме дали има дублирани headers в данните
            $duplicateHeaders = 0;
            for ($row = 2; $row <= min($finalTotalRows, 100); $row++) { // Проверяваме първите 100 реда
                $cellValue = $finalValidationSheet->getCellByColumnAndRow(1, $row)->getValue();
                if (!empty($cellValue) && (strpos($cellValue, 'ID') !== false || strpos($cellValue, 'Код') !== false) && $cellValue === $headerRow1Value) {
                    $duplicateHeaders++;
                }
            }

            $finalValidationSpreadsheet->disconnectWorksheets();
            unset($finalValidationSpreadsheet);

            F()->log->developer('XLSX Combine: Финален файл записан успешно: ' . basename($finalFilePath), __FILE__, __LINE__);
            F()->log->developer('XLSX Combine: Финална валидация - Общо редове: ' . $finalTotalRows . ', Колони: ' . $finalTotalColumns . ', Header валиден: ' . ($isHeaderValid ? 'ДА' : 'НЕ') . ', Дублирани headers: ' . $duplicateHeaders, __FILE__, __LINE__);

            if ($duplicateHeaders > 0) {
                F()->log->developer('XLSX Combine: ВНИМАНИЕ! Намерени ' . $duplicateHeaders . ' дублирани header реда в данните!', __FILE__, __LINE__);
            }

            if (!$isHeaderValid) {
                F()->log->developer('XLSX Combine: ВНИМАНИЕ! Header редът не изглежда валиден!', __FILE__, __LINE__);
            }

        } catch (\Exception $e) {
            F()->log->developer('XLSX Combine: Грешка при записване на финален файл: ' . $e->getMessage(), __FILE__, __LINE__);

            // Ако грешката е свързана с памет, опитваме алтернативния CSV подход
            if (strpos($e->getMessage(), 'memory') !== false || strpos($e->getMessage(), 'exhausted') !== false) {
                F()->log->developer('XLSX Combine: Memory грешка открита, превключване към CSV подход', __FILE__, __LINE__);

                // Освобождаваме текущия spreadsheet
                if (isset($finalSpreadsheet)) {
                    $finalSpreadsheet->disconnectWorksheets();
                    unset($finalSpreadsheet);
                }
                gc_collect_cycles();

                // Възстановяваме memory limit
                ini_set('memory_limit', $originalMemoryLimit);

                // Опитваме алтернативния подход
                return $this->combineXlsxFilesViaCsv($tempFiles, $finalFilePath, $tempDir);
            }

            throw new \Exception('Не може да се запише финалният XLSX файл: ' . $e->getMessage());
        } finally {
            // КРИТИЧНО: Възстановяваме оригиналния memory limit
            ini_set('memory_limit', $originalMemoryLimit);
            F()->log->developer('XLSX Combine: Memory limit възстановен на ' . $originalMemoryLimit, __FILE__, __LINE__);
        }

        return basename($finalFilePath);
    }

    /**
     * Алтернативен метод за обединяване на XLSX файлове чрез CSV конвертиране
     * Използва се при memory проблеми с директния XLSX подход
     */
    private function combineXlsxFilesViaCsv($tempFiles, $finalFilePath, $tempDir) {
        F()->log->developer('XLSX Combine: Използване на алтернативен CSV подход за memory efficiency', __FILE__, __LINE__);

        // Настройваме PhpSpreadsheet cache и за CSV подхода
        try {
            \PhpOffice\PhpSpreadsheet\Settings::setCache(new \PhpOffice\PhpSpreadsheet\Collection\Memory());
            F()->log->developer('XLSX Combine CSV: PhpSpreadsheet cache настроен', __FILE__, __LINE__);
        } catch (\Exception $e) {
            F()->log->developer('XLSX Combine CSV: Cache настройка неуспешна: ' . $e->getMessage(), __FILE__, __LINE__);
        }

        // Създаваме временен CSV файл
        $tempCsvPath = $tempDir . 'temp_combined.csv';
        $csvFile = fopen($tempCsvPath, 'w');
        if (!$csvFile) {
            throw new \Exception('Не може да се създаде временен CSV файл');
        }

        $headerWritten = false;
        $totalRows = 0;

        foreach ($tempFiles as $index => $tempFileName) {
            $tempFilePath = $tempDir . $tempFileName;

            if (!file_exists($tempFilePath)) {
                continue;
            }

            F()->log->developer('XLSX Combine CSV: Конвертиране на файл ' . ($index + 1) . '/' . count($tempFiles), __FILE__, __LINE__);

            try {
                // Зареждаме XLSX файла
                $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
                $spreadsheet = $reader->load($tempFilePath);
                $sheet = $spreadsheet->getActiveSheet();

                $highestRow = $sheet->getHighestRow();
                $highestColumn = $sheet->getHighestColumn();
                $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);

                // За първия файл записваме header-а и установяваме стандартния брой колони
                $startRow = 1;
                if ($headerWritten) {
                    $startRow = 2; // Пропускаме header за останалите файлове

                    // Проверяваме column consistency
                    if (!isset($standardColumns)) {
                        $standardColumns = $highestColumnIndex;
                    } elseif ($highestColumnIndex !== $standardColumns) {
                        F()->log->developer('XLSX Combine CSV: ВНИМАНИЕ! Файл има ' . $highestColumnIndex . ' колони, но стандартът е ' . $standardColumns, __FILE__, __LINE__);
                        $highestColumnIndex = min($highestColumnIndex, $standardColumns); // Използваме по-малкия брой
                    }
                } else {
                    $standardColumns = $highestColumnIndex; // Установяваме стандарта от първия файл
                    F()->log->developer('XLSX Combine CSV: Header установен с ' . $standardColumns . ' колони', __FILE__, __LINE__);
                }

                for ($row = $startRow; $row <= $highestRow; $row++) {
                    $rowData = [];
                    for ($col = 1; $col <= $highestColumnIndex; $col++) {
                        $cellValue = $sheet->getCellByColumnAndRow($col, $row)->getValue();
                        $rowData[] = $cellValue;
                    }
                    fputcsv($csvFile, $rowData);
                    $totalRows++;
                }

                if (!$headerWritten) {
                    $headerWritten = true;
                }

                // Освобождаваме паметта
                $spreadsheet->disconnectWorksheets();
                unset($spreadsheet);
                gc_collect_cycles();

            } catch (\Exception $e) {
                F()->log->developer('XLSX Combine CSV: Грешка при файл ' . $tempFileName . ': ' . $e->getMessage(), __FILE__, __LINE__);
                continue;
            }
        }

        fclose($csvFile);
        F()->log->developer('XLSX Combine CSV: CSV файл създаден с ' . $totalRows . ' реда', __FILE__, __LINE__);

        // Конвертираме CSV обратно в XLSX
        try {
            $csvReader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
            $csvReader->setDelimiter(',');
            $csvReader->setEnclosure('"');
            $csvReader->setSheetIndex(0);

            $spreadsheet = $csvReader->load($tempCsvPath);
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->setTitle('Products Export');

            // Автоматично оразмеряване на колоните
            $highestColumn = $sheet->getHighestColumn();
            $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);
            for ($col = 1; $col <= $highestColumnIndex; $col++) {
                $sheet->getColumnDimensionByColumn($col)->setAutoSize(true);
            }

            // Записваме финалния XLSX файл
            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
            $writer->save($finalFilePath);

            // Cleanup
            $spreadsheet->disconnectWorksheets();
            unset($spreadsheet);
            unlink($tempCsvPath); // Изтриваме временния CSV файл

            F()->log->developer('XLSX Combine CSV: Финален XLSX файл създаден успешно', __FILE__, __LINE__);

        } catch (\Exception $e) {
            F()->log->developer('XLSX Combine CSV: Грешка при CSV-to-XLSX конвертиране: ' . $e->getMessage(), __FILE__, __LINE__);
            throw new \Exception('Не може да се конвертира CSV в XLSX: ' . $e->getMessage());
        }

        return basename($finalFilePath);
    }

    /**
     * Обединява XML файлове
     */
    private function combineXmlFiles($tempFiles, $finalFilePath, $tempDir) {
        $finalFile = fopen($finalFilePath, 'w');
        if (!$finalFile) {
            throw new \Exception('Не може да се създаде финалният XML файл');
        }

        // Записваме XML заглавието
        fwrite($finalFile, "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
        fwrite($finalFile, "<products>\n");

        foreach ($tempFiles as $tempFileName) {
            $tempFilePath = $tempDir . $tempFileName;
            if (!file_exists($tempFilePath)) {
                continue;
            }

            $tempContent = file_get_contents($tempFilePath);
            if ($tempContent === false) {
                continue;
            }

            // Премахваме XML заглавието и root елементите от временните файлове
            $tempContent = preg_replace('/<\?xml[^>]*\?>/', '', $tempContent);
            $tempContent = preg_replace('/<\/?products[^>]*>/', '', $tempContent);
            $tempContent = trim($tempContent);

            if (!empty($tempContent)) {
                fwrite($finalFile, $tempContent . "\n");
            }
        }

        fwrite($finalFile, "</products>\n");
        fclose($finalFile);
        return basename($finalFilePath);
    }

    /**
     * Изчиства временните файлове
     */
    private function cleanupTempFiles($tempFiles) {
        $tempDir = DIR_UPLOAD . 'export_temp/';

        foreach ($tempFiles as $tempFileName) {
            $tempFilePath = $tempDir . $tempFileName;
            if (file_exists($tempFilePath)) {
                unlink($tempFilePath);
            }
        }
    }

    /**
     * Връща финалния експорт файл към браузера
     */
    private function returnFinalExportFile($fileName, $format) {
        $tempDir = DIR_UPLOAD . 'export_temp/';
        $filePath = $tempDir . $fileName;

        if (!file_exists($filePath)) {
            throw new \Exception('Финалният експорт файл не съществува');
        }

        // Четем съдържанието на файла
        $fileContent = file_get_contents($filePath);
        if ($fileContent === false) {
            throw new \Exception('Не може да се прочете финалният експорт файл');
        }

        // Определяме Content-Type според формата
        $contentType = $this->getContentTypeForFormat($format);

        // Изчистваме всички предишни изходи
        if (ob_get_level()) {
            ob_end_clean();
        }

        // Задаваме HTTP заглавията
        header('Content-Type: ' . $contentType);
        header('Content-Disposition: attachment; filename="' . $fileName . '"');
        header('Content-Length: ' . strlen($fileContent));
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        // Изпращаме файла
        echo $fileContent;

        // Изчистваме финалния файл
        unlink($filePath);

        exit;
    }

    /**
     * Логва използването на паметта за debug цели
     */
    private function logMemoryUsage($context = '') {
        if (function_exists('isDeveloper') && isDeveloper()) {
            $currentUsage = memory_get_usage(true);
            $peakUsage = memory_get_peak_usage(true);
            $usageMB = round($currentUsage / 1024 / 1024, 2);
            $peakMB = round($peakUsage / 1024 / 1024, 2);

            F()->log->developer("Memory usage {$context}: {$usageMB}MB current, {$peakMB}MB peak", __FILE__, __LINE__);
        }
    }
}

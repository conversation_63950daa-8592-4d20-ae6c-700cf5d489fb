<?php

namespace Theme25\Backend\Model\Catalog;

class Productexportcsv extends \Model {

    private $languages = [];
    private $languageCount = 0;
    private $currentLanguageId = 1;
    private $dataModel;
    private $fieldHelper;

    public function __construct($registry) {
        parent::__construct($registry);
        $this->dataModel = $this->registry->get('exportDataModel');
        $this->fieldHelper = $this->registry->get('exportFieldHelper');
    }

    /**
     * Задава централизираната fieldHelper инстанция
     */
    public function setFieldHelper($fieldHelper) {
        if($this->fieldHelper) return;
        $this->fieldHelper = $fieldHelper;
    }

    /**
     * Задава централизираната dataModel инстанция
     */
    public function setDataModel($dataModel) {
        if(!$this->dataModel) {
            $this->dataModel = $dataModel;
        }

        // Синхронизираме language_id ако е зададен
        if (is_callable([$this, 'getLanguageId']) && is_callable([$dataModel, 'setLanguageId'])) {
            $dataModel->setLanguageId($this->getLanguageId());
        }
    }

    /**
     * Генерира CSV файл с продукти използвайки централизирани данни
     */
    public function generateFile($products, $filePath, $languageId = null, $exportData = []) {
        // Проверяваме дали dataModel е зададен
        if (!$this->dataModel) {
            throw new \Exception('DataModel не е инициализиран в Productexportcsv');
        }
        // Инициализираме езиците и текущия език
        $this->initializeLanguages($languageId);

        // Включваме debug режима ако е developer
        if (function_exists('isDeveloper') && isDeveloper()) {
            $this->fieldHelper->setDebugMode(true);
        }

        // Диагностика на експорта
        $diagnosis = $this->fieldHelper->diagnoseExport($exportData, 'csv');

        // Обработваме данните за експорт и определяме избраните полета
        $selectedFields = $this->processExportData($exportData);

        // Запазваме текущата локализация и задаваме C локализация за стабилен CSV
        $originalLocale = setlocale(LC_ALL, 0);
        setlocale(LC_ALL, 'C');

        // Отваряме файла в binary режим за правилно записване на UTF-8 BOM
        $file = fopen($filePath, 'wb');

        if (!$file) {
            throw new \Exception('Не може да се създаде файлът за експорт');
        }

        // Добавяме UTF-8 BOM за правилно показване на кирилица в Excel
        fwrite($file, "\xEF\xBB\xBF");

        // Затваряме и отваряме отново в текстов режим за CSV операции
        fclose($file);
        $file = fopen($filePath, 'a');

        // Записваме заглавията с изрично задаване на запетая като разделител
        $headers = $this->getHeaders($selectedFields);
        $this->writeCSVRow($file, $headers);

        // Извличаме ID-тата на продуктите
        $productIds = array_column($products, 'product_id');

        // Подготвяме опциите за централизираното извличане на данни
        $options = $this->prepareDataOptions($exportData);

        // Извличаме всички данни централизирано с batch обработка
        $enrichedProducts = $this->dataModel->getProductsForExport($productIds, $options);

        // Записваме данните за продуктите
        foreach ($enrichedProducts as $product) {
            $rows = $this->prepareProductRows($product, $selectedFields);
            foreach ($rows as $row) {
                $this->writeCSVRow($file, $row);
            }
        }

        fclose($file);

        // Възстановяваме оригиналната локализация
        setlocale(LC_ALL, $originalLocale);
    }

    /**
     * Генерира данни за продукти за incremental експорт
     */
    public function generateProductData($products, $exportData = []) {
        // Проверяваме дали dataModel е зададен
        if (!$this->dataModel) {
            throw new \Exception('DataModel не е инициализиран в Productexportcsv');
        }

        // Включваме debug режима ако е developer
        if (function_exists('isDeveloper') && isDeveloper()) {
            $this->fieldHelper->setDebugMode(true);
        }

        // Обработваме данните за експорт и определяме избраните полета
        $selectedFields = $this->processExportData($exportData);

        // Извличаме ID-тата на продуктите
        $productIds = array_column($products, 'product_id');

        // Подготвяме опциите за централизираното извличане на данни
        $options = $this->prepareDataOptions($exportData);

        // Извличаме всички данни централизирано с batch обработка
        $enrichedProducts = $this->dataModel->getProductsForExport($productIds, $options);

        // Подготвяме данните за всички продукти
        $allProductData = [];
        foreach ($enrichedProducts as $product) {
            $rows = $this->prepareProductRows($product, $selectedFields);
            foreach ($rows as $productRow) {
                $allProductData[] = $productRow;
            }
        }

        return $allProductData;
    }

    /**
     * Записва CSV ред с изрично задаване на запетая като разделител
     */
    private function writeCSVRow($file, $data) {
        // Използваме ръчно форматиране за гарантиране на запетая като разделител
        $csvLine = '';
        $first = true;

        foreach ($data as $field) {
            if (!$first) {
                $csvLine .= ',';
            }

            // Конвертираме към string ако не е
            $field = (string)$field;

            // Escape специални символи
            $field = str_replace('"', '""', $field);

            // Обграждаме в кавички ако съдържа запетая, нов ред, кавички или HTML тагове
            if (strpos($field, ',') !== false ||
                strpos($field, "\n") !== false ||
                strpos($field, "\r") !== false ||
                strpos($field, '"') !== false ||
                strpos($field, "\t") !== false ||
                strpos($field, '<') !== false ||
                strpos($field, '>') !== false) {
                $csvLine .= '"' . $field . '"';
            } else {
                $csvLine .= $field;
            }

            $first = false;
        }

        fwrite($file, $csvLine . "\n");
    }

    /**
     * Връща всички налични полета за експорт
     */
    public function getAvailableFields() {
        return $this->fieldHelper->getFieldsForExport();
    }

    /**
     * Връща групираните полета за интерфейса
     */
    public function getGroupedFields() {
        return $this->fieldHelper->getAvailableFields();
    }

    /**
     * Връща заглавията на групите
     */
    public function getGroupLabels() {
        return $this->fieldHelper->getGroupLabels();
    }

    /**
     * Връща полетата по подразбиране
     */
    private function getDefaultFields() {
        return $this->fieldHelper->getDefaultFields();
    }

    /**
     * Подготвя опциите за централизираното извличане на данни
     */
    private function prepareDataOptions($exportData) {
        $options = [];

        // Проверяваме основните опции
        if (isset($exportData['basic_options'])) {
            $basicOptions = $exportData['basic_options'];

            $options['include_promo_price'] = !empty($basicOptions['include_promo_price']);
            $options['include_categories'] = !empty($basicOptions['include_categories']);
            $options['include_options'] = !empty($basicOptions['include_options']);
            $options['include_attributes'] = !empty($basicOptions['include_attributes']);
            $options['include_descriptions'] = !empty($basicOptions['include_descriptions']);
        }

        // Проверяваме избраните полета
        if (isset($exportData['selected_fields'])) {
            $selectedFields = $exportData['selected_fields'];

            if (in_array('additional_images', $selectedFields)) {
                $options['include_additional_images'] = true;
            }
        }

        return $options;
    }

    /**
     * Обработва данните за експорт и връща масив с избрани полета
     */
    private function processExportData($exportData) {
        // Ако няма данни за експорт, използваме всички полета по подразбиране
        if (empty($exportData)) {
            return $this->getDefaultFields();
        }

        // Ако данните са директно масив с полета (за обратна съвместимост)
        if (is_array($exportData) && !isset($exportData['type']) && !isset($exportData['basic_options'])) {
            return $exportData;
        }

        // Използваме централизирания метод от helper-а
        return $this->fieldHelper->processSelectedFields($exportData, 'csv');
    }

    /**
     * Инициализира езиците в системата и текущия език
     * МЕТОД ПРЕМАХНАТ - езиците се управляват централизирано от Productexportdata.php
     */
    private function initializeLanguages($languageId = null) {
        // Този метод е премахнат като част от рефакторирането
        // Езиците се управляват централизирано в Productexportdata.php
        // За обратна съвместимост запазваме основната логика

        $this->languages = [];
        $this->languageCount = 1; // Fallback стойност

        // Задаваме текущия език
        if ($languageId !== null) {
            $this->currentLanguageId = $languageId;
        } else {
            $this->currentLanguageId = 1; // Fallback
        }
    }

    /**
     * Връща заглавията за CSV файла
     */
    private function getHeaders($selectedFields) {
        // Използваме централизирания метод от helper-а
        return $this->fieldHelper->generateHeaders($selectedFields, $this->languages, 'csv');
    }

    /**
     * Подготвя редовете с данни за продукт използвайки предварително извлечени данни
     */
    private function prepareProductRows($product, $selectedFields) {
        $rows = [];

        // Използваме предварително извлечените описания
        $descriptions = isset($product['product_descriptions']) ? $product['product_descriptions'] : [];

        // Ако имаме само един език или няма описания, създаваме един ред
        if ($this->languageCount <= 1 || empty($descriptions)) {
            $row = $this->prepareProductRow($product, $descriptions, $selectedFields, true);
            $rows[] = $row;
        } else {
            // За всеки език създаваме отделен ред
            foreach ($this->languages as $langCode => $language) {
                $langData = $descriptions[$langCode] ?? [];
                $row = $this->prepareProductRow($product, [$langCode => $langData], $selectedFields, false);
                $rows[] = $row;
            }
        }

        return $rows;
    }

    /**
     * Подготвя един ред с данни за продукт
     */
    private function prepareProductRow($product, $descriptions, $selectedFields, $singleLanguage = true) {
        $row = [];
        $availableFields = $this->getAvailableFields();

        // Винаги добавяме product_id като първо поле
        $row[] = $product['product_id'] ?? '';

        // Винаги добавяме name ако е в избраните полета
        if (in_array('name', $selectedFields)) {
            // Получаваме името от описанията
            $currentData = !empty($descriptions) ? reset($descriptions) : [];
            $row[] = $this->dataModel->cleanText($currentData['name'] ?? '');
        }

        // Винаги добавяме model ако е в избраните полета
        if (in_array('model', $selectedFields)) {
            $row[] = $this->dataModel->cleanText($product['model'] ?? '');
        }

        // Добавяме основните полета (без product_id, name, model които вече са добавени)
        foreach ($availableFields['basic'] as $field => $label) {
            if (in_array($field, $selectedFields) && !in_array($field, ['product_id', 'name', 'model'])) {
                $value = $product[$field] ?? '';

                // Специална обработка за някои полета използвайки предварително извлечени данни
                if ($field === 'image' && !empty($value)) {
                    $value = $product['image_url'] ?? $this->dataModel->getImageUrl($value);
                } elseif ($field === 'additional_images') {
                    $images = $product['additional_images'] ?? [];
                    $value = implode(';', $images);
                } elseif ($field === 'quantity') {
                    $value = $this->dataModel->formatQuantity($value);
                } elseif ($field === 'promo_price') {
                    // Използваме предварително извлечената промо цена
                    $value = $this->dataModel->formatPrice($product['promo_price'] ?? 0.00);
                } elseif (in_array($field, ['price', 'weight', 'length', 'width', 'height'])) {
                    $value = $this->dataModel->formatPrice($value);
                }

                $row[] = $this->dataModel->cleanText($value);
            }
        }

        // Добавяме многоезичните полета (без name което вече е добавено)
        if ($singleLanguage) {
            // Един език - използваме данните от първия наличен език
            $currentData = !empty($descriptions) ? reset($descriptions) : [];
            foreach ($availableFields['multilingual'] as $field => $label) {
                if (in_array($field, $selectedFields) && $field !== 'name') {
                    if ($field === 'description') {
                        // За описанието запазваме HTML но го подготвяме за CSV
                        $row[] = $this->prepareDescriptionForCSV($currentData[$field] ?? '');
                    } else {
                        $row[] = $this->dataModel->cleanText($currentData[$field] ?? '');
                    }
                }
            }
        } else {
            // Повече езици - добавяме данни за всеки език със суфикси
            foreach ($this->languages as $langCode => $language) {
                $langData = $descriptions[$langCode] ?? [];
                foreach ($availableFields['multilingual'] as $field => $label) {
                    if (in_array($field, $selectedFields) && $field !== 'name') {
                        if ($field === 'description') {
                            // За описанието запазваме HTML но го подготвяме за CSV
                            $row[] = $this->prepareDescriptionForCSV($langData[$field] ?? '');
                        } else {
                            $row[] = $this->dataModel->cleanText($langData[$field] ?? '');
                        }
                    }
                }
            }
        }

        // Добавяме допълнителните полета използвайки предварително извлечени данни
        foreach ($availableFields['additional'] as $field => $label) {
            if (in_array($field, $selectedFields)) {
                switch ($field) {
                    case 'categories':
                        $categories = $product['product_categories'] ?? [];
                        $categoryPaths = [];
                        foreach ($categories as $category) {
                            $path = $category['path'] ?? [$category['category_name'] ?? ''];
                            $categoryPaths[] = implode(' > ', $path);
                        }
                        $row[] = implode(';', $categoryPaths);
                        break;
                    case 'attributes':
                        $attributes = $product['product_attributes'] ?? [];
                        $attributeStrings = [];
                        foreach ($attributes as $attr) {
                            $attributeStrings[] = ($attr['attribute_name'] ?? '') . ': ' . ($attr['attribute_text'] ?? '');
                        }
                        $row[] = implode(';', $attributeStrings);
                        break;
                    case 'options':
                        $options = $product['product_options'] ?? [];
                        $optionStrings = [];
                        foreach ($options as $opt) {
                            $optionStrings[] = ($opt['option_name'] ?? '') . ': ' . ($opt['value'] ?? '');
                        }
                        $row[] = implode(';', $optionStrings);
                        break;
                    default:
                        $row[] = '';
                }
            }
        }

        return $row;
    }

    /**
     * Подготвя description полето за CSV експорт (използва централизираната функция)
     */
    private function prepareDescriptionForCSV($description) {
        if (empty($description)) {
            return '';
        }

        // Използваме централизираната функция за почистване на текст
        return $this->dataModel->cleanText($description);
    }

    // cleanText метод премахнат - използваме централизирания от Productexportdata
}
<?php

namespace Theme25\Backend\Model\Catalog;

class Productexportxlsx extends \Model {

    private $languages = [];
    private $languageCount = 0;
    private $currentLanguageId = 1;
    private $dataModel;
    private $fieldHelper;

    public function __construct($registry) {
        parent::__construct($registry);

        defined('VENDORS_DIR') || define('VENDORS_DIR', DIR_SYSTEM . 'storage/vendor/');
        require_once(VENDORS_DIR . 'phpoffice/PhpSpreadsheet/autoload.php');

        $this->dataModel = $this->registry->get('exportDataModel');
        $this->fieldHelper = $this->registry->get('exportFieldHelper');
    }

    /**
     * Задава централизираната fieldHelper инстанция
     */
    public function setFieldHelper($fieldHelper) {
        if($this->fieldHelper) return;
        $this->fieldHelper = $fieldHelper;
    }

    /**
     * Задава централизираната dataModel инстанция
     */
    public function setDataModel($dataModel) {
        if(!$this->dataModel) {
            $this->dataModel = $dataModel;
        }

        // Синхронизираме language_id ако е зададен
        if (is_callable([$this, 'getLanguageId']) && is_callable([$dataModel, 'setLanguageId'])) {
            $dataModel->setLanguageId($this->getLanguageId());
        }
    }

    /**
     * Генерира XLSX файл с продукти
     */
    public function generateFile($products, $filePath, $languageId = null, $exportData = []) {
        // Проверяваме дали dataModel е зададен
        if (!$this->dataModel) {
            throw new \Exception('DataModel не е инициализиран в Productexportxlsx');
        }
        // Проверяваме дали PhpSpreadsheet е наличен
        if (!class_exists('\PhpOffice\PhpSpreadsheet\Spreadsheet')) {
            throw new \Exception('PhpSpreadsheet библиотеката не е налична. Моля, инсталирайте я чрез Composer.');
        }

        // Инициализираме езиците и текущия език
        $this->initializeLanguages($languageId);

        // Включваме debug режима ако е developer
        if (function_exists('isDeveloper') && isDeveloper()) {
            $this->fieldHelper->setDebugMode(true);
        }

        // Диагностика на експорта
        $diagnosis = $this->fieldHelper->diagnoseExport($exportData, 'xlsx');

        // Обработваме данните за експорт и определяме избраните полета
        $selectedFields = $this->processExportData($exportData);

        // Създаваме нов spreadsheet
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Products Export');

        // Записваме заглавията
        $headers = $this->getHeaders($selectedFields);
        $col = 1;
        foreach ($headers as $header) {
            $sheet->setCellValueByColumnAndRow($col, 1, $header);
            $col++;
        }

        // Стилизираме заглавията
        $headerRange = 'A1:' . \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(count($headers)) . '1';
        $sheet->getStyle($headerRange)->getFont()->setBold(true);
        $sheet->getStyle($headerRange)->getFill()
            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()->setARGB('FFE0E0E0');

        // Извличаме ID-тата на продуктите
        $productIds = array_column($products, 'product_id');

        // Подготвяме опциите за централизираното извличане на данни
        $options = $this->prepareDataOptions($exportData);

        // Извличаме всички данни централизирано с batch обработка
        $enrichedProducts = $this->dataModel->getProductsForExport($productIds, $options);

        // Записваме данните за продуктите
        $row = 2;
        foreach ($enrichedProducts as $product) {
            $rows = $this->prepareProductRows($product, $selectedFields);
            foreach ($rows as $productRow) {
                $col = 1;
                foreach ($productRow as $value) {
                    $sheet->setCellValueByColumnAndRow($col, $row, $value);
                    $col++;
                }
                $row++;
            }
        }

        // Автоматично оразмеряване на колоните
        foreach (range(1, count($headers)) as $columnIndex) {
            $sheet->getColumnDimensionByColumn($columnIndex)->setAutoSize(true);
        }

        // Записваме файла
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save($filePath);
    }

    /**
     * Генерира данни за продукти за incremental експорт
     */
    public function generateProductData($products, $exportData = []) {
        // Проверяваме дали dataModel е зададен
        if (!$this->dataModel) {
            throw new \Exception('DataModel не е инициализиран в Productexportxlsx');
        }

        // Включваме debug режима ако е developer
        if (function_exists('isDeveloper') && isDeveloper()) {
            $this->fieldHelper->setDebugMode(true);
        }

        // Обработваме данните за експорт и определяме избраните полета
        $selectedFields = $this->processExportData($exportData);

        // Извличаме ID-тата на продуктите
        $productIds = array_column($products, 'product_id');

        // Подготвяме опциите за централизираното извличане на данни
        $options = $this->prepareDataOptions($exportData);

        // Извличаме всички данни централизирано с batch обработка
        $enrichedProducts = $this->dataModel->getProductsForExport($productIds, $options);

        // Подготвяме данните за всички продукти
        $allProductData = [];
        foreach ($enrichedProducts as $product) {
            $rows = $this->prepareProductRows($product, $selectedFields);
            foreach ($rows as $productRow) {
                $allProductData[] = $productRow;
            }
        }

        return $allProductData;
    }

    /**
     * Връща всички налични полета за експорт
     */
    public function getAvailableFields() {
        return $this->fieldHelper->getFieldsForExport();
    }

    /**
     * Връща групираните полета за интерфейса
     */
    public function getGroupedFields() {
        return $this->fieldHelper->getAvailableFields();
    }

    /**
     * Връща заглавията на групите
     */
    public function getGroupLabels() {
        return $this->fieldHelper->getGroupLabels();
    }

    /**
     * Връща полетата по подразбиране
     */
    private function getDefaultFields() {
        return $this->fieldHelper->getDefaultFields();
    }

    /**
     * Подготвя опциите за централизираното извличане на данни
     */
    private function prepareDataOptions($exportData) {
        $options = [];

        // Проверяваме основните опции
        if (isset($exportData['basic_options'])) {
            $basicOptions = $exportData['basic_options'];

            $options['include_promo_price'] = !empty($basicOptions['include_promo_price']);
            $options['include_categories'] = !empty($basicOptions['include_categories']);
            $options['include_options'] = !empty($basicOptions['include_options']);
            $options['include_attributes'] = !empty($basicOptions['include_attributes']);
            $options['include_descriptions'] = !empty($basicOptions['include_descriptions']);
        }

        // Проверяваме избраните полета
        if (isset($exportData['selected_fields'])) {
            $selectedFields = $exportData['selected_fields'];

            if (in_array('additional_images', $selectedFields)) {
                $options['include_additional_images'] = true;
            }
        }

        return $options;
    }

    /**
     * Обработва данните за експорт и връща масив с избрани полета
     */
    private function processExportData($exportData) {
        // Ако няма данни за експорт, използваме всички полета по подразбиране
        if (empty($exportData)) {
            return $this->getDefaultFields();
        }

        // Ако данните са директно масив с полета (за обратна съвместимост)
        if (is_array($exportData) && !isset($exportData['type']) && !isset($exportData['basic_options'])) {
            return $exportData;
        }

        // Използваме централизирания метод от helper-а
        return $this->fieldHelper->processSelectedFields($exportData, 'xlsx');
    }

    /**
     * Инициализира езиците в системата и текущия език
     * МЕТОД ПРЕМАХНАТ - езиците се управляват централизирано от Productexportdata.php
     */
    private function initializeLanguages($languageId = null) {
        // Този метод е премахнат като част от рефакторирането
        // Езиците се управляват централизирано в Productexportdata.php
        // За обратна съвместимост запазваме основната логика

        $this->languages = [];
        $this->languageCount = 1; // Fallback стойност

        // Задаваме текущия език
        if ($languageId !== null) {
            $this->currentLanguageId = $languageId;
        } else {
            $this->currentLanguageId = 1; // Fallback
        }
    }

    /**
     * Връща заглавията за XLSX файла
     */
    private function getHeaders($selectedFields) {
        $headers = [];
        $availableFields = $this->getAvailableFields();

        // Винаги добавяме product_id като първо поле
        $headers[] = 'Продукт ID';

        // Винаги добавяме name ако не е вече добавено
        if (in_array('name', $selectedFields)) {
            $headers[] = 'Име';
        }

        // Винаги добавяме model ако не е вече добавено
        if (in_array('model', $selectedFields)) {
            $headers[] = 'Модел';
        }

        // Добавяме основните полета (без product_id, name, model които вече са добавени)
        foreach ($availableFields['basic'] as $field => $label) {
            if (in_array($field, $selectedFields) && !in_array($field, ['product_id', 'name', 'model'])) {
                $headers[] = $label;
            }
        }

        // Добавяме многоезичните полета (без name което вече е добавено)
        if ($this->languageCount <= 1) {
            // Един език - без суфикси
            foreach ($availableFields['multilingual'] as $field => $label) {
                if (in_array($field, $selectedFields) && $field !== 'name') {
                    $headers[] = $label;
                }
            }
        } else {
            // Повече езици - добавяме суфикси
            foreach ($this->languages as $langCode => $language) {
                foreach ($availableFields['multilingual'] as $field => $label) {
                    if (in_array($field, $selectedFields) && $field !== 'name') {
                        $headers[] = $label . ' (' . $language['name'] . ')';
                    }
                }
            }
        }

        // Добавяме допълнителните полета
        foreach ($availableFields['additional'] as $field => $label) {
            if (in_array($field, $selectedFields)) {
                $headers[] = $label;
            }
        }

        return $headers;
    }

    /**
     * Подготвя редовете с данни за продукт използвайки предварително извлечени данни
     */
    private function prepareProductRows($product, $selectedFields) {
        $rows = [];

        // Използваме предварително извлечените описания
        $descriptions = isset($product['product_descriptions']) ? $product['product_descriptions'] : [];

        // Ако имаме само един език или няма описания, създаваме един ред
        if ($this->languageCount <= 1 || empty($descriptions)) {
            $rows[] = $this->prepareProductRow($product, $descriptions, $selectedFields, true);
        } else {
            // Създаваме ред за всеки език
            $rows[] = $this->prepareProductRow($product, $descriptions, $selectedFields, false);
        }
        return $rows;
    }

    /**
     * Подготвя един ред с данни за продукт
     */
    private function prepareProductRow($product, $descriptions, $selectedFields, $singleLanguage = true) {
        $row = [];
        $availableFields = $this->getAvailableFields();

        // Винаги добавяме product_id като първо поле
        $row[] = $product['product_id'];

        // Винаги добавяме name ако е в избраните полета
        if (in_array('name', $selectedFields)) {
            // Получаваме името от описанията
            $description = null;
            if (!empty($descriptions)) {
                foreach ($descriptions as $desc) {
                    if ($desc['language_id'] == $this->currentLanguageId) {
                        $description = $desc;
                        break;
                    }
                }
                // Ако няма описание за текущия език, използваме първото налично
                if (!$description) {
                    $description = reset($descriptions);
                }
            }
            $row[] = $description['name'] ?? '';
        }

        // Винаги добавяме model ако е в избраните полета
        if (in_array('model', $selectedFields)) {
            $row[] = $product['model'] ?? '';
        }

        // Добавяме основните полета (без product_id, name, model които вече са добавени)
        foreach ($availableFields['basic'] as $field => $label) {
            if (in_array($field, $selectedFields) && !in_array($field, ['product_id', 'name', 'model'])) {
                switch ($field) {
                    case 'model':
                        $row[] = $product['model'] ?? '';
                        break;
                    case 'sku':
                        $row[] = $product['sku'] ?? '';
                        break;
                    case 'upc':
                        $row[] = $product['upc'] ?? '';
                        break;
                    case 'ean':
                        $row[] = $product['ean'] ?? '';
                        break;
                    case 'jan':
                        $row[] = $product['jan'] ?? '';
                        break;
                    case 'isbn':
                        $row[] = $product['isbn'] ?? '';
                        break;
                    case 'mpn':
                        $row[] = $product['mpn'] ?? '';
                        break;
                    case 'location':
                        $row[] = $product['location'] ?? '';
                        break;
                    case 'quantity':
                        $row[] = $this->dataModel->formatQuantity($product['quantity'] ?? 0);
                        break;
                    case 'stock_status':
                        $row[] = $product['stock_status'] ?? '';
                        break;
                    case 'image':
                        $row[] = $product['image_url'] ?? $this->dataModel->getImageUrl($product['image'] ?? '');
                        break;
                    case 'additional_images':
                        $images = $product['additional_images'] ?? [];
                        $row[] = implode(', ', $images);
                        break;
                    case 'manufacturer':
                        $row[] = $product['manufacturer'] ?? '';
                        break;
                    case 'shipping':
                        $row[] = $product['shipping'] ? 'Yes' : 'No';
                        break;
                    case 'price':
                        $row[] = $this->dataModel->formatPrice($product['price'] ?? 0);
                        break;
                    case 'promo_price':
                        $row[] = $this->dataModel->formatPrice($product['promo_price'] ?? 0.00);
                        break;
                    case 'points':
                        $row[] = $product['points'] ?? 0;
                        break;
                    case 'tax_class':
                        $row[] = $product['tax_class'] ?? '';
                        break;
                    case 'date_available':
                        $row[] = $product['date_available'] ?? '';
                        break;
                    case 'weight':
                        $row[] = number_format((float)($product['weight'] ?? 0), 2, '.', '');
                        break;
                    case 'weight_class':
                        $row[] = $product['weight_class'] ?? '';
                        break;
                    case 'length':
                        $row[] = number_format((float)($product['length'] ?? 0), 2, '.', '');
                        break;
                    case 'width':
                        $row[] = number_format((float)($product['width'] ?? 0), 2, '.', '');
                        break;
                    case 'height':
                        $row[] = number_format((float)($product['height'] ?? 0), 2, '.', '');
                        break;
                    case 'length_class':
                        $row[] = $product['length_class'] ?? '';
                        break;
                    case 'status':
                        $row[] = $product['status'] ? 'Enabled' : 'Disabled';
                        break;
                    case 'sort_order':
                        $row[] = $product['sort_order'] ?? 0;
                        break;
                    default:
                        $row[] = $product[$field] ?? '';
                        break;
                }
            }
        }

        // Добавяме многоезичните полета
        if ($singleLanguage) {
            // Един език - използваме текущия език
            $description = null;
            if (!empty($descriptions)) {
                foreach ($descriptions as $desc) {
                    if ($desc['language_id'] == $this->currentLanguageId) {
                        $description = $desc;
                        break;
                    }
                }
                // Ако няма описание за текущия език, използваме първото налично
                if (!$description) {
                    $description = reset($descriptions);
                }
            }

            foreach ($availableFields['multilingual'] as $field => $label) {
                if (in_array($field, $selectedFields) && $field !== 'name') {
                    switch ($field) {
                        case 'description':
                            $row[] = $this->prepareDescriptionForXLSX($description['description'] ?? '');
                            break;
                        case 'meta_title':
                            $row[] = $description['meta_title'] ?? '';
                            break;
                        case 'meta_description':
                            $row[] = $description['meta_description'] ?? '';
                            break;
                        case 'meta_keyword':
                            $row[] = $description['meta_keyword'] ?? '';
                            break;
                        case 'tag':
                            $row[] = $description['tag'] ?? '';
                            break;
                        default:
                            $row[] = '';
                            break;
                    }
                }
            }
        } else {
            // Повече езици - добавяме всички езици
            foreach ($this->languages as $langCode => $language) {
                $description = null;
                if (!empty($descriptions)) {
                    foreach ($descriptions as $desc) {
                        if ($desc['language_id'] == $language['language_id']) {
                            $description = $desc;
                            break;
                        }
                    }
                }

                foreach ($availableFields['multilingual'] as $field => $label) {
                    if (in_array($field, $selectedFields) && $field !== 'name') {
                        switch ($field) {
                            case 'description':
                                $row[] = $this->prepareDescriptionForXLSX($description['description'] ?? '');
                                break;
                            case 'meta_title':
                                $row[] = $description['meta_title'] ?? '';
                                break;
                            case 'meta_description':
                                $row[] = $description['meta_description'] ?? '';
                                break;
                            case 'meta_keyword':
                                $row[] = $description['meta_keyword'] ?? '';
                                break;
                            case 'tag':
                                $row[] = $description['tag'] ?? '';
                                break;
                            default:
                                $row[] = '';
                                break;
                        }
                    }
                }
            }
        }

        // Добавяме допълнителните полета
        foreach ($availableFields['additional'] as $field => $label) {
            if (in_array($field, $selectedFields)) {
                switch ($field) {
                    case 'categories':
                        $categories = $product['product_categories'] ?? [];
                        $categoryPaths = [];
                        foreach ($categories as $category) {
                            $path = $category['path'] ?? [$category['category_name'] ?? ''];
                            $categoryPaths[] = implode(' > ', $path);
                        }
                        $row[] = implode(', ', $categoryPaths);
                        break;
                    case 'attributes':
                        $attributes = $product['product_attributes'] ?? [];
                        $attributeStrings = [];
                        foreach ($attributes as $attr) {
                            $attributeStrings[] = ($attr['attribute_name'] ?? '') . ': ' . ($attr['attribute_text'] ?? '');
                        }
                        $row[] = implode(', ', $attributeStrings);
                        break;
                    case 'options':
                        $options = $product['product_options'] ?? [];
                        $optionStrings = [];
                        foreach ($options as $opt) {
                            $optionStrings[] = ($opt['option_name'] ?? '') . ': ' . ($opt['value'] ?? '');
                        }
                        $row[] = implode('; ', $optionStrings);
                        break;
                    default:
                        $row[] = '';
                        break;
                }
            }
        }

        return $row;
    }

    // SQL методи премахнати - използваме централизирани данни от Productexportdata

    // getProductPromoPrice метод премахнат - използваме централизирани данни

    // Всички SQL методи премахнати - използваме централизирани данни от Productexportdata

    // getProductAttributes и getProductOptions методи премахнати

    /**
     * Подготвя description полето за XLSX експорт (използва централизираната функция)
     */
    private function prepareDescriptionForXLSX($description) {
        if (empty($description)) {
            return '';
        }

        // Използваме централизираната функция за почистване на текст
        return $this->dataModel->cleanText($description);
    }

    // cleanText метод премахнат - използваме централизирания от Productexportdata
}